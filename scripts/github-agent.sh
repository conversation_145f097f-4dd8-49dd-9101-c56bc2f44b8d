#!/bin/bash

# GitHub Agent - Automated PR Management for Midnight Burner
# Handles all GitHub operations: PR creation, branch management, timing

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MIDNIGHT_BURNER_DIR="$PROJECT_ROOT"

# Load configuration
if [ -f "$MIDNIGHT_BURNER_DIR/.env" ]; then
    source "$MIDNIGHT_BURNER_DIR/.env"
fi

# Default configuration
PR_PUSH_INTERVAL_HOURS=${PR_PUSH_INTERVAL_HOURS:-4}
PR_MAX_FILES_PER_PR=${PR_MAX_FILES_PER_PR:-8}
PR_MIN_FILES_PER_PR=${PR_MIN_FILES_PER_PR:-2}
GITHUB_USERNAME=${GITHUB_USERNAME:-""}
GITHUB_TOKEN=${GITHUB_TOKEN:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[GitHub Agent]${NC} $1"
}

error() {
    echo -e "${RED}[GitHub Agent ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[GitHub Agent]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[GitHub Agent WARNING]${NC} $1"
}

# Function to check if we're in a git repository
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "Not in a git repository. GitHub Agent requires a git repository."
        exit 1
    fi
}

# Function to check git configuration
check_git_config() {
    if [ -z "$GITHUB_TOKEN" ]; then
        error "GITHUB_TOKEN not set. Please configure GitHub token in .env file."
        exit 1
    fi
    
    if [ -z "$(git config user.name)" ]; then
        if [ -n "$GITHUB_USERNAME" ]; then
            git config user.name "$GITHUB_USERNAME"
            log "Set git user.name to $GITHUB_USERNAME"
        else
            error "Git user.name not configured. Set GITHUB_USERNAME in .env or configure git."
            exit 1
        fi
    fi
    
    if [ -z "$(git config user.email)" ]; then
        if [ -n "$GITHUB_EMAIL" ]; then
            git config user.email "$GITHUB_EMAIL"
            log "Set git user.email to $GITHUB_EMAIL"
        else
            error "Git user.email not configured. Set GITHUB_EMAIL in .env or configure git."
            exit 1
        fi
    fi
}

# Function to get repository information
get_repo_info() {
    local remote_url=$(git config --get remote.origin.url)
    if [[ $remote_url =~ github\.com[:/]([^/]+)/([^/.]+) ]]; then
        REPO_OWNER="${BASH_REMATCH[1]}"
        REPO_NAME="${BASH_REMATCH[2]}"
        log "Repository: $REPO_OWNER/$REPO_NAME"
    else
        error "Could not parse GitHub repository from remote URL: $remote_url"
        exit 1
    fi
}

# Function to check for uncommitted changes
check_uncommitted_changes() {
    if ! git diff --quiet || ! git diff --cached --quiet; then
        local changed_files=$(git diff --name-only; git diff --cached --name-only)
        local file_count=$(echo "$changed_files" | wc -l)
        
        log "Found $file_count uncommitted files:"
        echo "$changed_files" | sed 's/^/  /'
        
        return 0  # Has changes
    else
        log "No uncommitted changes found"
        return 1  # No changes
    fi
}

# Function to create a feature branch
create_feature_branch() {
    local branch_name="feature/midnight-burner-$(date +%Y%m%d-%H%M%S)"
    
    log "Creating feature branch: $branch_name"
    git checkout -b "$branch_name"
    
    echo "$branch_name"
}

# Function to generate PR notes
generate_pr_notes() {
    local branch_name="$1"
    local pr_notes_file="PR_NOTES.md"
    
    log "Generating PR notes: $pr_notes_file"
    
    cat > "$pr_notes_file" << EOF
# PR Notes - Midnight Burner Autonomous Development
**Generated**: $(date '+%Y-%m-%d %H:%M:%S')
**Branch**: $branch_name
**Agent**: GitHub Agent

## 🤖 Autonomous Development Summary

This PR was created automatically by Midnight Burner's GitHub Agent as part of the continuous development cycle.

## 📊 Changes Overview

$(git diff --cached --stat)

## 🔍 Detailed Changes

### Modified Files:
$(git diff --cached --name-only | sed 's/^/- /')

### Key Changes:
$(git log --oneline $(git merge-base HEAD origin/main)..HEAD | sed 's/^/- /')

## 🧪 Testing Status

- [ ] Automated tests passing
- [ ] Code review completed by secondary swarm
- [ ] Integration tests verified
- [ ] Performance impact assessed

## 🚀 Deployment Notes

- **Ready for merge**: $(if git diff --cached --quiet; then echo "❌ No changes"; else echo "✅ Changes staged"; fi)
- **Breaking changes**: None identified
- **Migration required**: No
- **Environment updates**: None required

## 🔗 Related Issues

<!-- Links to Linear issues will be added automatically if Linear integration is enabled -->

---
*This PR was created by Midnight Burner - The AI that never sleeps*
*🔥 Repository: https://github.com/m-check1B/midnight-burner*
EOF

    git add "$pr_notes_file"
    success "PR notes generated and staged"
}

# Function to commit changes
commit_changes() {
    local commit_message="$1"
    
    log "Committing changes with message: $commit_message"
    git commit -m "$commit_message

🤖 Auto-generated by Midnight Burner GitHub Agent
📅 $(date '+%Y-%m-%d %H:%M:%S')
🔥 Autonomous development cycle"
    
    success "Changes committed successfully"
}

# Function to push branch and create PR
create_pull_request() {
    local branch_name="$1"
    local pr_title="$2"
    local pr_body="$3"
    
    log "Pushing branch: $branch_name"
    git push -u origin "$branch_name"
    
    log "Creating pull request..."
    
    # Use GitHub CLI if available
    if command -v gh &> /dev/null; then
        gh pr create \
            --title "$pr_title" \
            --body "$pr_body" \
            --head "$branch_name" \
            --base main
        success "Pull request created using GitHub CLI"
    else
        # Fallback to API call using curl
        local api_url="https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/pulls"
        local json_payload=$(jq -n \
            --arg title "$pr_title" \
            --arg body "$pr_body" \
            --arg head "$branch_name" \
            --arg base "main" \
            '{title: $title, body: $body, head: $head, base: $base}')
        
        local response=$(curl -s -X POST \
            -H "Authorization: token $GITHUB_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            -d "$json_payload" \
            "$api_url")
        
        local pr_url=$(echo "$response" | jq -r '.html_url // empty')
        if [ -n "$pr_url" ]; then
            success "Pull request created: $pr_url"
        else
            error "Failed to create pull request. Response: $response"
            return 1
        fi
    fi
}

# Function to run the GitHub agent cycle
run_github_cycle() {
    log "Starting GitHub Agent cycle..."
    
    check_git_repo
    check_git_config
    get_repo_info
    
    if check_uncommitted_changes; then
        log "Processing uncommitted changes..."
        
        # Create feature branch
        local branch_name=$(create_feature_branch)
        
        # Stage all changes
        git add .
        
        # Generate PR notes
        generate_pr_notes "$branch_name"
        
        # Create commit
        local commit_message="feat: Midnight Burner autonomous development cycle
        
- Automated feature development and bug fixes
- Code quality improvements and optimizations  
- Documentation updates and maintenance
- Testing enhancements and coverage improvements"
        
        commit_changes "$commit_message"
        
        # Create PR
        local pr_title="🔥 Midnight Burner: Autonomous Development Cycle - $(date '+%Y-%m-%d %H:%M')"
        local pr_body="## 🤖 Autonomous Development Update

This PR contains changes from Midnight Burner's autonomous development cycle.

### 📋 What's Changed
- Continuous feature development and improvements
- Automated bug fixes and optimizations
- Code quality enhancements
- Documentation updates
- Test coverage improvements

### 🔍 Review Notes
All changes have been processed by our dual-swarm AI system:
- **Primary Swarm**: Feature development and implementation
- **Secondary Swarm**: Testing, documentation, and quality assurance
- **GitHub Agent**: PR management and git operations

### 🚀 Ready for Review
This PR is ready for human review and contains reasonably-sized changes ($(git diff --cached --name-only | wc -l) files modified).

---
*Generated by Midnight Burner GitHub Agent*
*Repository: https://github.com/m-check1B/midnight-burner*"
        
        create_pull_request "$branch_name" "$pr_title" "$pr_body"
        
        # Switch back to main branch
        git checkout main
        success "GitHub cycle completed successfully"
        
    else
        log "No changes to process. Cycle complete."
    fi
}

# Function to start GitHub agent daemon
start_daemon() {
    local target_repo="${1:-$(pwd)}"
    local log_file="$MIDNIGHT_BURNER_DIR/logs/github-agent.log"
    
    log "Starting GitHub Agent daemon for repository: $target_repo"
    log "PR push interval: every $PR_PUSH_INTERVAL_HOURS hours"
    log "Log file: $log_file"
    
    mkdir -p "$(dirname "$log_file")"
    
    cd "$target_repo"
    
    while true; do
        {
            echo "=== GitHub Agent Cycle Started at $(date) ==="
            run_github_cycle
            echo "=== GitHub Agent Cycle Completed at $(date) ==="
            echo ""
        } >> "$log_file" 2>&1
        
        log "Cycle completed. Sleeping for $PR_PUSH_INTERVAL_HOURS hours..."
        sleep $((PR_PUSH_INTERVAL_HOURS * 3600))
    done
}

# Main script logic
case "${1:-daemon}" in
    "cycle")
        run_github_cycle
        ;;
    "daemon")
        start_daemon "$2"
        ;;
    "test")
        log "Testing GitHub Agent configuration..."
        check_git_repo
        check_git_config
        get_repo_info
        log "GitHub Agent configuration is valid"
        ;;
    *)
        echo "Usage: $0 {cycle|daemon|test} [target_repo]"
        echo ""
        echo "Commands:"
        echo "  cycle     - Run one GitHub cycle (commit, branch, PR)"
        echo "  daemon    - Start continuous GitHub agent daemon"
        echo "  test      - Test GitHub agent configuration"
        echo ""
        echo "Examples:"
        echo "  $0 cycle                    # Run one cycle in current repo"
        echo "  $0 daemon /path/to/repo     # Start daemon for specific repo"
        echo "  $0 test                     # Test configuration"
        exit 1
        ;;
esac