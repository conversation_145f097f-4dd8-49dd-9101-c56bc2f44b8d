# Perpetual Worker + Linear Integration Example

This example shows how to set up Perpetual Worker with Linear's MCP server for complete project management automation.

## 🎯 What You Get

With Linear integration, Perpetual Worker becomes a **fully autonomous project management system** that:
- **Automatically finds and assigns Linear issues** to work on
- **Updates issue status in real-time** as development progresses
- **Adds detailed progress comments** to keep stakeholders informed
- **Links pull requests to Linear issues** automatically
- **Creates new issues** for bugs or improvements discovered during development
- **Prioritizes work** based on Linear issue priority and urgency

## 🚀 Setup Process

### 1. Prerequisites
- Linear workspace with API access
- Node.js 18+ installed
- Claude Code with MCP support

### 2. Get Your Linear API Key
1. Go to [Linear Settings > API](https://linear.app/settings/api)
2. Create a new Personal API Key
3. Copy the key (starts with `lin_api_`)

### 3. Install Perpetual Worker with Linear
```bash
# Clone the repository
git clone https://github.com/m-check1B/perpetual-worker.git
cd perpetual-worker

# Set your Linear API key
export LINEAR_API_KEY="lin_api_your_key_here"

# Install with Linear integration
./install.sh

# Setup Linear MCP integration
./scripts/linear-mcp-integration.sh setup

# Test the integration
./scripts/linear-mcp-integration.sh test
```

### 4. Configure Your Linear Workspace
Edit `config/linear-mcp.json`:
```json
{
  "linear_config": {
    "workspace": {
      "name": "Your Company",
      "id": "your-workspace-id"
    },
    "teams": [
      {
        "name": "Engineering",
        "id": "your-team-id",
        "key": "ENG"
      }
    ],
    "perpetual_worker": {
      "user_id": "perpetual-worker-user-id",
      "label_name": "perpetual-worker",
      "auto_assign": true,
      "status_updates": true
    }
  }
}
```

### 5. Start the System
```bash
./start-perpetual.sh
```

## 🔄 How It Works

### Autonomous Issue Management Workflow

1. **Issue Discovery** 🔍
   - AI searches Linear for high-priority, unassigned issues
   - Uses `linear_search_issues priority:urgent,high state:todo`
   - Filters by team and project relevance

2. **Issue Assignment** 👤
   - AI claims the most important issue
   - Updates assignee to "perpetual-worker"
   - Changes status to "In Progress"
   - Adds initial comment: "🤖 Starting work on this issue"

3. **Development Process** 💻
   - AI reads issue description and requirements
   - Plans implementation approach
   - Begins coding with proper context

4. **Progress Updates** 📝
   - Regular comments added to Linear issue:
     - "✅ Completed initial setup"
     - "🔧 Implementing authentication logic"
     - "🧪 Adding comprehensive tests"
     - "📝 Updating documentation"

5. **PR Creation** 🔗
   - Creates pull request with proper title
   - Links to Linear issue: "Closes ENG-123"
   - Includes detailed PR_NOTES.md
   - Updates Linear issue status to "In Review"

6. **Completion** ✅
   - After PR merge, updates issue to "Done"
   - Adds completion comment with summary
   - Automatically moves to next high-priority issue

## 📊 Example Linear Integration in Action

### Linear Issue: ENG-123 "Add User Authentication"

**Initial State:**
- Status: `Todo`
- Priority: `High`
- Assignee: `Unassigned`

**AI Discovers Issue:**
```
🔍 AI searches: linear_search_issues priority:high state:todo
📋 Finds: ENG-123 - Add User Authentication
🎯 AI decides: This is high priority, claiming it
```

**AI Claims Issue:**
```
👤 linear_update_issue id:ENG-123 assignee:perpetual-worker state:in_progress
💬 linear_add_comment issue:ENG-123 "🤖 Starting implementation of user authentication system"
```

**Development Progress:**
```
Day 1:
💬 "✅ Created authentication middleware and JWT utilities"
💬 "🔧 Implementing login/register endpoints"

Day 2: 
💬 "🧪 Added comprehensive test suite with 95% coverage"
💬 "📝 Updated API documentation with new endpoints"
💬 "🔗 Created PR #47: Add user authentication system (ENG-123)"
```

**PR Link Update:**
```
🔗 linear_add_comment issue:ENG-123 "Pull Request created: https://github.com/company/repo/pull/47"
📊 linear_update_issue id:ENG-123 state:in_review
```

**Completion:**
```
✅ linear_update_issue id:ENG-123 state:done
💬 linear_add_comment issue:ENG-123 "🎉 Authentication system completed! 
   - JWT-based authentication implemented
   - Login/register endpoints added
   - Comprehensive test coverage
   - API documentation updated
   - PR merged and deployed"
```

## 🎯 Linear Workflow Patterns

### Bug Fixes
```
🐛 AI finds: linear_search_issues label:bug priority:high
🔧 AI works on bug reproduction and fix
📝 AI updates: "Bug reproduced, implementing fix"
✅ AI completes: "Bug fixed and tested"
```

### Feature Development
```
⭐ AI finds: linear_search_issues label:feature state:ready
🏗️ AI plans: Breaks down feature into sub-tasks
📈 AI updates: Regular progress milestones
🚀 AI delivers: Complete feature with tests and docs
```

### Technical Debt
```
🔨 AI creates: linear_create_issue for technical debt found
⚡ AI prioritizes: Updates priority based on impact
🧹 AI refactors: Systematic code improvement
📊 AI reports: Performance and maintainability gains
```

## 📈 Benefits of Linear Integration

### For Project Managers
- **Real-time visibility** into development progress
- **Automatic status updates** without manual tracking
- **Detailed progress comments** for stakeholder updates
- **Predictable velocity** with 24/7 development

### For Developers
- **No manual issue management** - AI handles all Linear updates
- **Clear development priorities** based on Linear issue priority
- **Proper documentation** of all development decisions
- **Seamless PR-to-issue linking**

### For Teams
- **Consistent workflow** across all development
- **Reduced context switching** - AI maintains focus
- **Better issue coverage** - Nothing gets forgotten
- **Improved communication** through detailed Linear comments

## 🛠️ Advanced Configuration

### Custom Priority Mapping
```json
"priority_mapping": {
  "urgent": "critical",
  "high": "high", 
  "medium": "medium",
  "low": "low"
}
```

### Team-Specific Workflows
```json
"teams": [
  {
    "name": "Backend",
    "focus": "api,database,performance",
    "labels": ["backend", "api"]
  },
  {
    "name": "Frontend", 
    "focus": "ui,ux,components",
    "labels": ["frontend", "ui"]
  }
]
```

### Custom Status Transitions
```json
"status_mapping": {
  "todo": "ready_for_dev",
  "in_progress": "developing", 
  "in_review": "code_review",
  "done": "deployed"
}
```

## 🎮 Monitoring Linear Integration

### View Integration Status
```bash
./scripts/linear-mcp-integration.sh status
```

### Check Recent Linear Activity
```bash
# View Linear-related comments in logs
grep -i "linear" logs/perpetual-worker.log

# Monitor issue assignments
./scripts/linear-mcp-integration.sh test
```

### Linear Dashboard Integration
- All Linear issues show Perpetual Worker activity
- Comments provide detailed development timeline
- Status updates reflect real development progress
- PRs are automatically linked and tracked

## 🎯 Result: Autonomous Project Management

With Linear integration, you get:
- **Zero manual issue tracking** - AI handles everything
- **Perfect project visibility** - Stakeholders see real-time progress  
- **Consistent development workflow** - Every issue follows the same pattern
- **Complete development lifecycle** - From issue assignment to deployment
- **Scalable across teams** - Works with multiple Linear teams and projects

**Your Linear workspace becomes a live dashboard of autonomous development progress!** 🚀