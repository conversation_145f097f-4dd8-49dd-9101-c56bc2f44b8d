#!/usr/bin/env bats

# End-to-end test for complete development cycle

setup() {
  export MIDNIGHT_BURNER_ROOT="/home/<USER>/github/midnight-burner"
  export TEST_MODE=1
  export E2E_TIMEOUT=30
  export TEST_PROJECT_DIR="$MIDNIGHT_BURNER_ROOT/test-project"
  
  # Clean up any previous test project
  rm -rf "$TEST_PROJECT_DIR"
}

teardown() {
  # Clean up test artifacts
  rm -rf "$TEST_PROJECT_DIR"
  tmux kill-session -t test-e2e-worker 2>/dev/null || true
  unset TEST_MODE
}

@test "complete development cycle from start to finish" {
  skip "Requires full Claude API access"
  
  # Create test project
  mkdir -p "$TEST_PROJECT_DIR"
  cd "$TEST_PROJECT_DIR"
  
  # Initialize with simple task
  cat > task.md <<EOF
Create a simple Hello World Node.js application with:
- Express server
- Basic test
- README documentation
EOF
  
  # Start Midnight Burner for test project
  export TMUX_SESSION="test-e2e-worker"
  export PROJECT_DIR="$TEST_PROJECT_DIR"
  
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --project "$TEST_PROJECT_DIR"
  [ "$status" -eq 0 ]
  
  # Wait for initial setup
  sleep 5
  
  # Verify files were created
  [ -f "$TEST_PROJECT_DIR/package.json" ]
  [ -f "$TEST_PROJECT_DIR/README.md" ]
  [ -f "$TEST_PROJECT_DIR/index.js" ] || [ -f "$TEST_PROJECT_DIR/server.js" ]
}

@test "system recovers from process failure" {
  # Start system
  export TMUX_SESSION="test-e2e-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Kill a pane to simulate failure
  tmux kill-pane -t test-e2e-worker:0.1 2>/dev/null || true
  
  # Wait for health monitor to detect
  sleep 5
  
  # Check if system recovered
  pane_count=$(tmux list-panes -t test-e2e-worker 2>/dev/null | wc -l)
  [ "$pane_count" -eq 4 ] || [ "$pane_count" -eq 0 ]
}

@test "configuration changes are applied dynamically" {
  # Start with default config
  export TMUX_SESSION="test-e2e-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Modify config
  cat > "$MIDNIGHT_BURNER_ROOT/config/test-dynamic.yml" <<EOF
response_interval: 5
health_check_interval: 5
memory_threshold: 90
EOF
  
  # Signal config reload
  tmux send-keys -t test-e2e-worker:0.3 "reload-config" C-m 2>/dev/null || true
  
  # Wait for reload
  sleep 2
  
  # Verify config was loaded (check logs)
  grep -q "reload" "$MIDNIGHT_BURNER_ROOT/logs/health-monitor.log" || true
}

@test "logs are properly rotated" {
  # Create large log files
  mkdir -p "$MIDNIGHT_BURNER_ROOT/logs"
  for log in orchestrator artificial-user health-monitor; do
    dd if=/dev/zero of="$MIDNIGHT_BURNER_ROOT/logs/$log.log" bs=1M count=12 2>/dev/null || true
  done
  
  # Start system
  export TMUX_SESSION="test-e2e-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Check if logs were rotated
  [ -f "$MIDNIGHT_BURNER_ROOT/logs/orchestrator.log.1" ] || \
  [ -f "$MIDNIGHT_BURNER_ROOT/logs/artificial-user.log.1" ] || \
  [ -f "$MIDNIGHT_BURNER_ROOT/logs/health-monitor.log.1" ]
}

@test "stop script cleanly shuts down system" {
  # Start system
  export TMUX_SESSION="test-e2e-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Stop system
  run bash "$MIDNIGHT_BURNER_ROOT/stop-perpetual.sh" --session test-e2e-worker
  [ "$status" -eq 0 ]
  
  # Verify session is gone
  run tmux has-session -t test-e2e-worker
  [ "$status" -ne 0 ]
}

@test "Linear integration works correctly" {
  skip "Requires Linear API key"
  
  # Set up Linear test
  export LINEAR_API_KEY="test-key"
  export TMUX_SESSION="test-e2e-worker"
  
  # Start with Linear integration
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --linear-enabled
  
  # Should start successfully
  [ "$status" -eq 0 ] || [[ "$output" =~ "Linear" ]]
}

@test "GitHub integration creates PRs" {
  skip "Requires GitHub setup"
  
  # Test GitHub agent
  export GITHUB_TOKEN="test-token"
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/github-agent.sh" --test-pr
  
  # Should handle PR creation
  [ "$status" -eq 0 ] || [[ "$output" =~ "pull request" ]]
}

@test "metrics are collected and stored" {
  # Start system
  export TMUX_SESSION="test-e2e-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Wait for metrics collection
  sleep 5
  
  # Check metrics file
  [ -f "$MIDNIGHT_BURNER_ROOT/.claude-flow/metrics/system-metrics.json" ]
  
  # Verify metrics content
  grep -q "memoryUsagePercent" "$MIDNIGHT_BURNER_ROOT/.claude-flow/metrics/system-metrics.json"
}

@test "restart script maintains session continuity" {
  # Start system
  export TMUX_SESSION="test-e2e-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Create marker file to test continuity
  echo "test-marker" > "$MIDNIGHT_BURNER_ROOT/state/test-marker"
  
  # Restart system
  run bash "$MIDNIGHT_BURNER_ROOT/restart-perpetual.sh" --session test-e2e-worker
  [ "$status" -eq 0 ]
  
  # Verify marker persists
  [ -f "$MIDNIGHT_BURNER_ROOT/state/test-marker" ]
  
  # Cleanup
  rm -f "$MIDNIGHT_BURNER_ROOT/state/test-marker"
}

@test "system handles multiple projects" {
  skip "Advanced feature test"
  
  # Create two test projects
  mkdir -p "$MIDNIGHT_BURNER_ROOT/test-project-1"
  mkdir -p "$MIDNIGHT_BURNER_ROOT/test-project-2"
  
  # Start instances for both
  export TMUX_SESSION="test-project-1"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --project "$MIDNIGHT_BURNER_ROOT/test-project-1" || true
  
  export TMUX_SESSION="test-project-2"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --project "$MIDNIGHT_BURNER_ROOT/test-project-2" || true
  
  # Both should be running
  tmux has-session -t test-project-1
  tmux has-session -t test-project-2
  
  # Cleanup
  tmux kill-session -t test-project-1 2>/dev/null || true
  tmux kill-session -t test-project-2 2>/dev/null || true
  rm -rf "$MIDNIGHT_BURNER_ROOT/test-project-1" "$MIDNIGHT_BURNER_ROOT/test-project-2"
}