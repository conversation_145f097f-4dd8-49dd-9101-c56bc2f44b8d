# Contributing to Perpetual Worker

Thank you for your interest in contributing to Perpetual Worker! This document provides guidelines for contributing to the project.

## 🚀 Quick Start

1. Fork the repository
2. Clone your fork: `git clone https://github.com/YOUR_USERNAME/perpetual-worker.git`
3. Create a feature branch: `git checkout -b feature-name`
4. Make your changes
5. Test your changes: `./test-perpetual.sh`
6. Commit your changes: `git commit -m "feat: add new feature"`
7. Push to your fork: `git push origin feature-name`
8. Create a Pull Request

## 📋 Development Guidelines

### Code Style
- Use consistent bash scripting practices
- Add comments for complex logic
- Follow existing naming conventions
- Keep scripts modular and reusable

### Testing
- Test all changes on a clean system
- Verify installation script works from scratch
- Test with different project types (web-app, api, library)
- Ensure backward compatibility

### Documentation
- Update README.md for new features
- Add examples for new project types
- Update configuration documentation
- Include clear commit messages

## 🐛 Bug Reports

When reporting bugs, please include:
- Operating system and version
- Node.js version
- Claude Flow version
- Steps to reproduce
- Expected vs actual behavior
- Relevant log files

## 💡 Feature Requests

For new features, please:
- Check existing issues first
- Describe the use case
- Explain the expected behavior
- Consider backward compatibility
- Propose implementation approach

## 🧪 Testing

### Manual Testing
```bash
# Test installation
./install.sh

# Test basic functionality
./start-perpetual.sh
./scripts/status.sh
./stop-perpetual.sh

# Test with different project types
./scripts/setup-perpetual.sh /path/to/web-app --type web-app
./scripts/setup-perpetual.sh /path/to/api --type api
```

### Automated Testing
```bash
# Run test suite (when available)
./test-perpetual.sh

# Test configuration validation
./scripts/validate-config.sh
```

## 📁 Project Structure

```
perpetual-worker/
├── README.md              # Main documentation
├── install.sh            # Main installation script
├── start-perpetual.sh    # System startup
├── stop-perpetual.sh     # System shutdown
├── restart-perpetual.sh  # System restart
├── scripts/              # Core system scripts
├── prompts/              # Default AI prompts
├── config/               # Configuration files
├── examples/             # Usage examples
└── docs/                 # Additional documentation
```

## 🔧 Core Components

### Installation Script (`install.sh`)
- Checks prerequisites
- Installs dependencies
- Creates directory structure
- Sets up configuration

### Core Scripts (`scripts/`)
- `perpetual-orchestrator.sh` - Main system orchestrator
- `artificial-user.sh` - AI user simulation
- `health-monitor.sh` - System monitoring
- `status.sh` - System status checking

### Prompts (`prompts/`)
- `primary-swarm.txt` - Main development instructions
- `secondary-swarm.txt` - Support swarm behavior

## 🎯 Contribution Areas

### High Priority
- Improved error handling and recovery
- Better configuration validation
- Enhanced monitoring and metrics
- Cross-platform compatibility

### Medium Priority
- Additional project type templates
- Advanced customization options
- Performance optimizations
- Documentation improvements

### Low Priority
- Web UI for monitoring
- Integration with other AI tools
- Advanced scheduling features
- Multi-language support

## 📝 Commit Message Format

Use conventional commits:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions/changes
- `chore:` - Maintenance tasks

Examples:
```
feat: add support for Python projects
fix: resolve tmux session cleanup issue
docs: update installation instructions
```

## 🤝 Code Review Process

1. All changes require a pull request
2. Maintain backward compatibility
3. Include tests for new features
4. Update documentation as needed
5. Respond to review feedback promptly

## 📞 Getting Help

- **Issues**: [GitHub Issues](https://github.com/m-check1B/perpetual-worker/issues)
- **Discussions**: [GitHub Discussions](https://github.com/m-check1B/perpetual-worker/discussions)
- **Email**: <EMAIL>

## 📜 License

By contributing to Perpetual Worker, you agree that your contributions will be licensed under the MIT License.