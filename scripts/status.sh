#!/usr/bin/env bash
# Check Perpetual Worker Status

SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🔄 Perpetual Worker Status"
echo "========================="
echo ""

# Check session status
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    echo "📺 Tmux Session: ✅ Active ($SESSION_NAME)"
    echo "   Panes: $(tmux list-panes -t "$SESSION_NAME" | wc -l)"
else
    echo "📺 Tmux Session: ❌ Not running"
fi

# Check processes
process_count=$(pgrep -f "claude-flow" | wc -l)
echo "🤖 Claude Flow Processes: $process_count"

# Check logs
if [ -f "$PROJECT_ROOT/logs/orchestrator.log" ]; then
    last_activity=$(tail -n1 "$PROJECT_ROOT/logs/orchestrator.log" | cut -d']' -f1 | tr -d '[')
    echo "📝 Last Activity: $last_activity"
fi

echo ""
echo "🎮 Control Commands:"
echo "   View system: tmux attach -t $SESSION_NAME"
echo "   Stop system: $PROJECT_ROOT/stop-perpetual.sh"
echo "   Restart: $PROJECT_ROOT/restart-perpetual.sh"
