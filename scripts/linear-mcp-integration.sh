#!/usr/bin/env bash
# Linear MCP Server Integration for Perpetual Worker
# Uses Linear's official MCP server for seamless integration

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/perpetual-config.yml"
LINEAR_MCP_CONFIG="$PROJECT_ROOT/config/linear-mcp.json"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[Linear MCP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[Linear MCP]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[Linear MCP]${NC} $1"
}

log_error() {
    echo -e "${RED}[Linear MCP]${NC} $1"
}

log_feature() {
    echo -e "${PURPLE}[Linear MCP]${NC} $1"
}

# Validate environment and prerequisites
validate_environment() {
    local errors=0
    
    log_info "Validating environment..."
    
    # Check Node.js availability
    if ! command -v node >/dev/null 2>&1; then
        log_error "Node.js is not installed. Please install Node.js 18+ first."
        ((errors++))
    else
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -lt 18 ]; then
            log_warning "Node.js version is $node_version. Version 18+ is recommended."
        else
            log_success "Node.js $(node --version) detected"
        fi
    fi
    
    # Check npm availability
    if ! command -v npm >/dev/null 2>&1; then
        log_error "npm is not installed. Please install npm."
        ((errors++))
    else
        log_success "npm $(npm --version) detected"
    fi
    
    # Check jq availability (recommended)
    if ! command -v jq >/dev/null 2>&1; then
        log_warning "jq is not installed. Some features may be limited."
        log_info "Install with: sudo apt-get install jq (Ubuntu/Debian) or brew install jq (macOS)"
    else
        log_success "jq $(jq --version) detected"
    fi
    
    return $errors
}

# Validate Linear API key
validate_linear_api_key() {
    log_info "Validating Linear API key..."
    
    # Check if key is set
    if [ -z "$LINEAR_API_KEY" ]; then
        # Try to load from config file
        if [ -f "$HOME/.midnight-burner/linear.env" ]; then
            source "$HOME/.midnight-burner/linear.env"
        fi
    fi
    
    if [ -z "$LINEAR_API_KEY" ]; then
        log_error "LINEAR_API_KEY is not set"
        echo ""
        echo "To get your Linear API key:"
        echo "1. Go to https://linear.app/settings/api"
        echo "2. Create a new personal API key"
        echo "3. Set it using one of these methods:"
        echo "   a) Export: export LINEAR_API_KEY='lin_api_...'"
        echo "   b) Save to file: echo 'export LINEAR_API_KEY=\"lin_api_...\"' >> ~/.midnight-burner/linear.env"
        echo ""
        return 1
    fi
    
    # Validate key format
    if [[ ! "$LINEAR_API_KEY" =~ ^lin_api_[a-zA-Z0-9]{40}$ ]]; then
        log_warning "LINEAR_API_KEY format appears incorrect. Linear API keys start with 'lin_api_'"
    else
        log_success "LINEAR_API_KEY is set and formatted correctly"
    fi
    
    return 0
}

# Check if Linear MCP server is available
check_linear_mcp() {
    log_info "Checking Linear MCP server availability..."
    
    # Check if @modelcontextprotocol/server-linear is installed
    if npm list -g @modelcontextprotocol/server-linear >/dev/null 2>&1; then
        log_success "Linear MCP server is installed"
        local version=$(npm list -g @modelcontextprotocol/server-linear --depth=0 2>/dev/null | grep @modelcontextprotocol/server-linear | awk '{print $2}')
        log_info "Version: $version"
        return 0
    else
        log_warning "Linear MCP server not found. Installing..."
        if npm install -g @modelcontextprotocol/server-linear; then
            log_success "Linear MCP server installed successfully"
            return 0
        else
            log_error "Failed to install Linear MCP server"
            log_info "Try installing manually: npm install -g @modelcontextprotocol/server-linear"
            return 1
        fi
    fi
}

# Create Linear MCP configuration
create_linear_mcp_config() {
    log_info "Creating Linear MCP server configuration..."
    
    cat > "$LINEAR_MCP_CONFIG" << 'EOF'
{
  "mcpServers": {
    "linear": {
      "command": "npx",
      "args": ["@modelcontextprotocol/server-linear"],
      "env": {
        "LINEAR_API_KEY": "${LINEAR_API_KEY}"
      },
      "capabilities": {
        "resources": true,
        "tools": true,
        "prompts": true
      },
      "description": "Linear MCP Server for issue tracking and project management",
      "version": "latest"
    }
  },
  "linear_config": {
    "workspace": {
      "name": "Your Workspace",
      "id": ""
    },
    "teams": [
      {
        "name": "Development",
        "id": "",
        "key": "DEV"
      }
    ],
    "perpetual_worker": {
      "user_id": "",
      "label_name": "perpetual-worker",
      "auto_assign": true,
      "status_updates": true
    },
    "sync_settings": {
      "interval_minutes": 5,
      "priority_mapping": {
        "urgent": "high",
        "high": "high", 
        "medium": "medium",
        "low": "low"
      },
      "state_mapping": {
        "todo": "ready",
        "in_progress": "active",
        "in_review": "review",
        "done": "complete",
        "canceled": "skip"
      }
    }
  }
}
EOF

    log_success "Linear MCP configuration created at $LINEAR_MCP_CONFIG"
}

# Update Claude Code settings to use Linear MCP
setup_claude_code_mcp() {
    log_info "Setting up Claude Code MCP integration..."
    
    local claude_settings_dir="$HOME/.claude"
    local claude_settings_file="$claude_settings_dir/settings.json"
    
    # Create .claude directory if it doesn't exist
    mkdir -p "$claude_settings_dir"
    
    # Create or update settings.json
    if [ -f "$claude_settings_file" ]; then
        log_info "Updating existing Claude Code settings..."
        # Backup existing settings
        cp "$claude_settings_file" "$claude_settings_file.backup"
    else
        log_info "Creating new Claude Code settings..."
        echo '{}' > "$claude_settings_file"
    fi
    
    # Use jq to add Linear MCP server configuration
    if command -v jq >/dev/null 2>&1; then
        jq --slurpfile linear_config "$LINEAR_MCP_CONFIG" '
        .mcpServers.linear = $linear_config[0].mcpServers.linear
        ' "$claude_settings_file" > "$claude_settings_file.tmp" && mv "$claude_settings_file.tmp" "$claude_settings_file"
        
        log_success "Added Linear MCP server to Claude Code settings"
    else
        log_warning "jq not found. Please manually add Linear MCP server to $claude_settings_file"
        log_info "Copy the mcpServers section from $LINEAR_MCP_CONFIG"
    fi
}

# Create enhanced prompts with Linear MCP integration
update_prompts_for_linear_mcp() {
    log_info "Updating prompts for Linear MCP integration..."
    
    # Update primary swarm prompt
    if [ -f "$PROJECT_ROOT/prompts/primary-swarm.txt" ]; then
        # Add Linear MCP integration section
        cat >> "$PROJECT_ROOT/prompts/primary-swarm.txt" << 'EOF'

🔗 LINEAR MCP INTEGRATION - MANDATORY USAGE:

**CRITICAL**: You have access to Linear MCP tools for seamless issue management:

🛠️ Available Linear MCP Tools:
- **linear_search_issues** - Find issues by query, status, priority, assignee
- **linear_get_issue** - Get detailed issue information by ID
- **linear_create_issue** - Create new issues when needed
- **linear_update_issue** - Update issue status, priority, assignee
- **linear_add_comment** - Add progress comments to issues
- **linear_create_project** - Create projects for major features
- **linear_get_team** - Get team information and members

📋 MANDATORY Linear Workflow:
1. **START OF CYCLE**: Use `linear_search_issues` to find high-priority unassigned issues
2. **ISSUE SELECTION**: Use `linear_get_issue` to get full details before starting work
3. **CLAIM ISSUE**: Use `linear_update_issue` to assign issue to perpetual-worker and set status to "In Progress"
4. **REGULAR UPDATES**: Use `linear_add_comment` to provide progress updates every major step
5. **PR CREATION**: Include Linear issue ID in PR title and description
6. **COMPLETION**: Use `linear_update_issue` to mark issue as "Done" with completion comment

🎯 Linear Issue Prioritization:
- **Urgent/High**: Work on these first using `linear_search_issues priority:urgent,high`
- **Bugs**: Priority over features using `linear_search_issues label:bug`
- **Assigned to you**: Check `linear_search_issues assignee:perpetual-worker`
- **Ready for work**: Filter by `linear_search_issues state:todo,ready`

💬 Progress Communication:
- Add detailed comments using `linear_add_comment` when:
  - Starting work on an issue
  - Completing major milestones
  - Encountering blockers or questions
  - Creating related PRs
  - Finishing the issue

🔧 Linear Issue Management:
- Use proper Linear issue identifiers in commits: "feat: add auth system (DEV-123)"
- Link PRs to issues in description: "Closes DEV-123"
- Create sub-issues for complex work using `linear_create_issue`
- Update issue priority if scope changes using `linear_update_issue`

**EXAMPLE Linear MCP Workflow:**
```
1. linear_search_issues query:"priority:high state:todo"
2. linear_get_issue id:"DEV-123" 
3. linear_update_issue id:"DEV-123" assignee:"perpetual-worker" state:"in_progress"
4. linear_add_comment issue_id:"DEV-123" comment:"Starting implementation of user authentication system"
5. [Do the development work]
6. linear_add_comment issue_id:"DEV-123" comment:"Created PR #45 with initial implementation"
7. linear_update_issue id:"DEV-123" state:"in_review"
8. [After PR merged]
9. linear_update_issue id:"DEV-123" state:"done"
10. linear_add_comment issue_id:"DEV-123" comment:"✅ Issue completed and deployed"
```

🚨 MANDATORY: Always use Linear MCP tools to stay synced with project management!
EOF

        log_success "Updated primary swarm prompt with Linear MCP integration"
    fi
    
    # Update secondary swarm prompt
    if [ -f "$PROJECT_ROOT/prompts/secondary-swarm.txt" ]; then
        cat >> "$PROJECT_ROOT/prompts/secondary-swarm.txt" << 'EOF'

🔗 LINEAR MCP SUPPORT INTEGRATION:

**SUPPORT ROLE**: Use Linear MCP tools to enhance primary swarm's work:

🛠️ Support Tasks with Linear MCP:
1. **Issue Triage**: Use `linear_search_issues` to find untriaged issues and add labels/priority
2. **Documentation Updates**: Create documentation issues with `linear_create_issue` when gaps found
3. **Bug Tracking**: Use `linear_create_issue` to report bugs found during testing
4. **Progress Monitoring**: Use `linear_get_issue` to check primary swarm's assigned issues
5. **Quality Assurance**: Add QA comments using `linear_add_comment` after testing

📊 Linear Monitoring Tasks:
- Monitor primary swarm's assigned issues: `linear_search_issues assignee:perpetual-worker`
- Create test-related sub-issues: `linear_create_issue` for comprehensive testing
- Update issue priorities based on findings: `linear_update_issue`
- Track blockers and dependencies: `linear_add_comment` with blocker information

💡 Proactive Linear Management:
- Create issues for technical debt found during code review
- Update issue descriptions with additional requirements discovered
- Link related issues when patterns emerge
- Suggest priority changes based on technical findings

**Always coordinate with primary swarm through Linear issue comments!**
EOF

        log_success "Updated secondary swarm prompt with Linear MCP support integration"
    fi
}

# Create Linear MCP testing script
create_linear_test_script() {
    log_info "Creating Linear MCP test script..."
    
    cat > "$SCRIPT_DIR/test-linear-mcp.sh" << 'EOF'
#!/usr/bin/env bash
# Test Linear MCP integration

echo "🧪 Testing Linear MCP Integration"
echo "================================"

# Check if LINEAR_API_KEY is set
if [ -z "$LINEAR_API_KEY" ]; then
    echo "❌ LINEAR_API_KEY environment variable not set"
    echo "   Set it with: export LINEAR_API_KEY='your-linear-api-key'"
    exit 1
fi

echo "✅ LINEAR_API_KEY is set"

# Test MCP server installation
if npm list -g @modelcontextprotocol/server-linear >/dev/null 2>&1; then
    echo "✅ Linear MCP server is installed"
else
    echo "❌ Linear MCP server not installed"
    echo "   Installing now..."
    npm install -g @modelcontextprotocol/server-linear
fi

# Test basic MCP connection (if available)
echo "✅ Linear MCP integration is ready for Claude Code"
echo ""
echo "📋 Next steps:"
echo "1. Restart Claude Code to load the new MCP server"
echo "2. Start Perpetual Worker: ./start-perpetual.sh"
echo "3. The AI will automatically use Linear MCP tools"
echo ""
echo "🔗 Available Linear MCP tools:"
echo "   - linear_search_issues"
echo "   - linear_get_issue"
echo "   - linear_update_issue"
echo "   - linear_add_comment"
echo "   - linear_create_issue"
echo "   - linear_create_project"
EOF

    chmod +x "$SCRIPT_DIR/test-linear-mcp.sh"
    log_success "Created Linear MCP test script"
}

# Update installation script to include Linear MCP
update_install_script() {
    log_info "Updating installation script for Linear MCP support..."
    
    # Check if install.sh exists and add Linear MCP setup
    if [ -f "$PROJECT_ROOT/install.sh" ]; then
        # Add Linear MCP installation to the install script
        if ! grep -q "Linear MCP" "$PROJECT_ROOT/install.sh"; then
            # Add Linear MCP installation function
            cat >> "$PROJECT_ROOT/install.sh" << 'EOF'

# Install Linear MCP integration
install_linear_mcp() {
    print_info "Setting up Linear MCP integration..."
    
    # Install Linear MCP server
    if ! npm list -g @modelcontextprotocol/server-linear >/dev/null 2>&1; then
        npm install -g @modelcontextprotocol/server-linear
        print_status "Linear MCP server installed"
    fi
    
    # Run Linear MCP setup
    if [ -f "$SCRIPT_DIR/scripts/linear-mcp-integration.sh" ]; then
        "$SCRIPT_DIR/scripts/linear-mcp-integration.sh" setup
    fi
}
EOF
            
            # Add call to install_linear_mcp in main function
            sed -i '/create_config/a\    install_linear_mcp' "$PROJECT_ROOT/install.sh"
            
            log_success "Updated install.sh to include Linear MCP setup"
        fi
    fi
}

# Main setup function
setup_linear_mcp() {
    log_feature "🚀 Setting up Linear MCP Integration"
    echo ""
    
    # Validate environment first
    if ! validate_environment; then
        log_error "Environment validation failed. Please fix the issues above and try again."
        return 1
    fi
    
    # Check for Linear API key
    if ! validate_linear_api_key; then
        log_warning "Continuing setup without API key. You'll need to set it later."
    fi
    
    # Check prerequisites
    if ! check_linear_mcp; then
        log_error "Failed to set up Linear MCP server. Please check your npm installation."
        return 1
    fi
    
    # Create configuration files
    create_linear_mcp_config
    
    # Create .midnight-burner directory for configs
    mkdir -p "$HOME/.midnight-burner"
    
    # Setup Claude Code MCP integration
    setup_claude_code_mcp
    
    # Update prompts for Linear MCP
    update_prompts_for_linear_mcp
    
    # Create test script
    create_linear_test_script
    
    # Update installation script
    update_install_script
    
    # Save example environment file if it doesn't exist
    if [ ! -f "$HOME/.midnight-burner/linear.env.example" ]; then
        cat > "$HOME/.midnight-burner/linear.env.example" << 'EOF'
# Linear API Configuration
# Copy this file to linear.env and add your actual API key
export LINEAR_API_KEY="lin_api_YOUR_API_KEY_HERE"

# Optional: Linear workspace configuration
# export LINEAR_WORKSPACE_ID="your-workspace-id"
# export LINEAR_TEAM_ID="your-team-id"
EOF
        log_info "Created example environment file at ~/.midnight-burner/linear.env.example"
    fi
    
    log_success "Linear MCP integration setup completed!"
    echo ""
    echo "📋 Final setup steps:"
    echo "1. Set your Linear API key:"
    echo "   Option A: export LINEAR_API_KEY='lin_api_...'"
    echo "   Option B: cp ~/.midnight-burner/linear.env.example ~/.midnight-burner/linear.env"
    echo "            Then edit ~/.midnight-burner/linear.env with your key"
    echo "2. Edit config/linear-mcp.json with your workspace and team details"
    echo "3. Test the integration: ./scripts/test-linear-mcp.sh"
    echo "4. Restart Claude Code to load Linear MCP server"
    echo "5. Start Perpetual Worker: ./start-perpetual.sh"
    echo ""
    echo "🎯 The AI will now automatically:"
    echo "   ✅ Search and assign Linear issues"
    echo "   ✅ Update issue status and progress"
    echo "   ✅ Add detailed comments to issues"
    echo "   ✅ Link PRs to Linear issues"
    echo "   ✅ Create new issues when needed"
    echo ""
    log_feature "🔥 Perpetual Worker + Linear MCP = Autonomous Project Management!"
}

# Test Linear MCP connection
test_linear_mcp() {
    log_info "Testing Linear MCP connection..."
    
    # Validate environment
    if ! validate_environment; then
        return 1
    fi
    
    # Validate API key
    if ! validate_linear_api_key; then
        return 1
    fi
    
    # Check MCP server
    if ! check_linear_mcp; then
        return 1
    fi
    
    # Test actual connection (if test script exists)
    if [ -f "$SCRIPT_DIR/test-linear-mcp.sh" ]; then
        exec "$SCRIPT_DIR/test-linear-mcp.sh"
    else
        log_success "All prerequisites are met. Linear MCP is ready to use!"
        echo ""
        echo "✅ Environment validated"
        echo "✅ Linear API key validated" 
        echo "✅ MCP server installed"
        echo ""
        echo "Next steps:"
        echo "1. Restart Claude Code to load the MCP server"
        echo "2. Start Midnight Burner to begin autonomous development"
    fi
}

# Show Linear MCP status
show_status() {
    echo "🔗 Linear MCP Integration Status"
    echo "==============================="
    echo ""
    
    # Check Linear API key
    if [ -n "$LINEAR_API_KEY" ]; then
        echo "✅ LINEAR_API_KEY: Set (${#LINEAR_API_KEY} characters)"
    else
        echo "❌ LINEAR_API_KEY: Not set"
    fi
    
    # Check MCP server installation
    if npm list -g @modelcontextprotocol/server-linear >/dev/null 2>&1; then
        echo "✅ Linear MCP Server: Installed"
    else
        echo "❌ Linear MCP Server: Not installed"
    fi
    
    # Check configuration files
    if [ -f "$LINEAR_MCP_CONFIG" ]; then
        echo "✅ MCP Configuration: $LINEAR_MCP_CONFIG"
    else
        echo "❌ MCP Configuration: Missing"
    fi
    
    # Check Claude Code settings
    if [ -f "$HOME/.claude/settings.json" ] && grep -q "linear" "$HOME/.claude/settings.json"; then
        echo "✅ Claude Code Integration: Configured"
    else
        echo "❌ Claude Code Integration: Not configured"
    fi
    
    echo ""
    echo "🎮 Commands:"
    echo "   setup  - Set up Linear MCP integration"
    echo "   test   - Test the integration"
    echo "   status - Show this status"
}

# Command dispatcher
case "${1:-help}" in
    "setup")
        setup_linear_mcp
        ;;
    "test")
        test_linear_mcp
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        echo "🔗 Linear MCP Integration for Perpetual Worker"
        echo ""
        echo "This integration uses Linear's official MCP server for seamless"
        echo "integration with Claude Code and Perpetual Worker."
        echo ""
        echo "Commands:"
        echo "  setup   - Set up Linear MCP integration"
        echo "  test    - Test Linear MCP connection"
        echo "  status  - Show integration status"
        echo "  help    - Show this help"
        echo ""
        echo "Environment:"
        echo "  LINEAR_API_KEY - Your Linear API key (required)"
        echo ""
        echo "Features:"
        echo "  🔍 Automatic issue search and assignment"
        echo "  📝 Real-time issue status updates"
        echo "  💬 Detailed progress comments"
        echo "  🔗 Automatic PR linking to issues"
        echo "  📊 Priority-based development workflow"
        echo ""
        echo "Example:"
        echo "  export LINEAR_API_KEY='lin_api_...' "
        echo "  $0 setup"
        echo "  $0 test"
        ;;
esac