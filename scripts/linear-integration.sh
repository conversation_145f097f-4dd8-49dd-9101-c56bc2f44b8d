#!/usr/bin/env bash
# Linear Integration for Perpetual Worker
# Connects the autonomous development system with Linear project management

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/perpetual-config.yml"
LINEAR_CONFIG="$PROJECT_ROOT/config/linear-config.yml"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[Linear]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[Linear]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[Linear]${NC} $1"
}

log_error() {
    echo -e "${RED}[Linear]${NC} $1"
}

# Check if Linear CLI is installed
check_linear_cli() {
    if ! command -v linear >/dev/null 2>&1; then
        log_warning "Linear CLI not found. Installing..."
        npm install -g @linear/cli
        log_success "Linear CLI installed"
    else
        log_info "Linear CLI found: $(linear --version)"
    fi
}

# Initialize Linear configuration
init_linear_config() {
    if [ ! -f "$LINEAR_CONFIG" ]; then
        log_info "Creating Linear configuration..."
        cat > "$LINEAR_CONFIG" << 'EOF'
# Linear Integration Configuration for Perpetual Worker

linear:
  # API Configuration
  api_key: ""  # Your Linear API key (set via environment variable LINEAR_API_KEY)
  team_id: ""  # Your Linear team ID
  workspace_id: ""  # Your Linear workspace ID
  
  # Issue Management
  issue_sync:
    enabled: true
    sync_interval: 300  # seconds (5 minutes)
    auto_assign: true   # Auto-assign issues to perpetual worker
    
  # Priority Mapping (Linear priority -> Perpetual Worker priority)
  priority_mapping:
    urgent: "high"
    high: "high"
    medium: "medium"
    low: "low"
    
  # Status Mapping (Linear status -> Development action)
  status_mapping:
    todo: "analyze"
    in_progress: "continue"
    in_review: "review"
    done: "complete"
    canceled: "skip"
    
  # Labels for Perpetual Worker
  labels:
    perpetual_worker: "perpetual-worker"
    auto_assigned: "auto-assigned"
    ai_generated: "ai-generated"
    
  # PR Integration
  pr_integration:
    enabled: true
    auto_link_issues: true
    update_status: true
    add_comments: true
    
  # Filtering
  filters:
    include_labels: []  # Only sync issues with these labels (empty = all)
    exclude_labels: ["wontfix", "duplicate"]
    include_states: ["todo", "in_progress", "in_review"]
    assignee_filter: "perpetual-worker"  # Only sync issues assigned to this user
EOF
        log_success "Linear configuration created at $LINEAR_CONFIG"
        log_warning "Please edit $LINEAR_CONFIG and add your Linear API key and team details"
    else
        log_info "Linear configuration already exists"
    fi
}

# Fetch issues from Linear
fetch_linear_issues() {
    local limit=${1:-50}
    log_info "Fetching issues from Linear (limit: $limit)..."
    
    if [ -z "$LINEAR_API_KEY" ]; then
        log_error "LINEAR_API_KEY environment variable not set"
        return 1
    fi
    
    # Create issues cache directory
    mkdir -p "$PROJECT_ROOT/cache/linear"
    
    # Fetch issues using Linear CLI
    linear issue list \
        --limit "$limit" \
        --format json \
        --filter "state.type:active" \
        > "$PROJECT_ROOT/cache/linear/issues.json"
    
    local issue_count=$(jq length "$PROJECT_ROOT/cache/linear/issues.json" 2>/dev/null || echo "0")
    log_success "Fetched $issue_count issues from Linear"
}

# Convert Linear issues to Perpetual Worker format
convert_linear_issues() {
    log_info "Converting Linear issues to Perpetual Worker format..."
    
    local issues_file="$PROJECT_ROOT/cache/linear/issues.json"
    local priorities_file="$PROJECT_ROOT/LINEAR_PRIORITIES.md"
    
    if [ ! -f "$issues_file" ]; then
        log_error "No Linear issues found. Run fetch first."
        return 1
    fi
    
    # Create Linear priorities file
    cat > "$priorities_file" << 'EOF'
# Linear Integration - Development Priorities

**Auto-generated from Linear issues** - Last updated: DATE_PLACEHOLDER

## 🚨 High Priority Issues

URGENT_ISSUES_PLACEHOLDER

## 📋 Medium Priority Issues

HIGH_ISSUES_PLACEHOLDER

## 📝 Low Priority Issues

MEDIUM_ISSUES_PLACEHOLDER

## ℹ️ Information

- **Total issues**: TOTAL_COUNT_PLACEHOLDER
- **Last sync**: DATE_PLACEHOLDER
- **Linear workspace**: WORKSPACE_PLACEHOLDER
- **Team**: TEAM_PLACEHOLDER

## 🔄 Issue Status Legend

- 🆕 **Todo** - Ready for development
- 🔄 **In Progress** - Currently being worked on
- 👀 **In Review** - Awaiting review
- ✅ **Done** - Completed
- ❌ **Canceled** - Will not be completed

EOF

    # Process issues with jq and update the priorities file
    if command -v jq >/dev/null 2>&1; then
        local current_date=$(date '+%Y-%m-%d %H:%M:%S')
        local total_issues=$(jq length "$issues_file")
        
        # Get urgent/high priority issues
        local urgent_issues=$(jq -r '.[] | select(.priority == "urgent" or .priority == "high") | "- **\(.identifier)**: \(.title)\n  - Status: \(.state.name)\n  - Priority: \(.priority)\n  - URL: \(.url)\n"' "$issues_file")
        
        # Get medium priority issues  
        local medium_issues=$(jq -r '.[] | select(.priority == "medium") | "- **\(.identifier)**: \(.title)\n  - Status: \(.state.name)\n  - Priority: \(.priority)\n  - URL: \(.url)\n"' "$issues_file")
        
        # Get low priority issues
        local low_issues=$(jq -r '.[] | select(.priority == "low" or .priority == null) | "- **\(.identifier)**: \(.title)\n  - Status: \(.state.name)\n  - Priority: \(.priority // "none")\n  - URL: \(.url)\n"' "$issues_file")
        
        # Update the priorities file
        sed -i "s/DATE_PLACEHOLDER/$current_date/g" "$priorities_file"
        sed -i "s/TOTAL_COUNT_PLACEHOLDER/$total_issues/g" "$priorities_file"
        sed -i "s|URGENT_ISSUES_PLACEHOLDER|$urgent_issues|g" "$priorities_file"
        sed -i "s|HIGH_ISSUES_PLACEHOLDER|$medium_issues|g" "$priorities_file"
        sed -i "s|MEDIUM_ISSUES_PLACEHOLDER|$low_issues|g" "$priorities_file"
        
        log_success "Created LINEAR_PRIORITIES.md with $total_issues issues"
    else
        log_warning "jq not found. Install jq for full Linear integration."
    fi
}

# Update Linear issue status
update_linear_issue() {
    local issue_id="$1"
    local status="$2"
    local comment="$3"
    
    log_info "Updating Linear issue $issue_id to status: $status"
    
    if [ -z "$LINEAR_API_KEY" ]; then
        log_error "LINEAR_API_KEY environment variable not set"
        return 1
    fi
    
    # Map status to Linear state
    local linear_state=""
    case "$status" in
        "started") linear_state="In Progress" ;;
        "completed") linear_state="Done" ;;
        "reviewing") linear_state="In Review" ;;
        "blocked") linear_state="Blocked" ;;
        *) linear_state="In Progress" ;;
    esac
    
    # Update issue status
    linear issue update "$issue_id" --state "$linear_state"
    
    # Add comment if provided
    if [ -n "$comment" ]; then
        linear comment create --issue "$issue_id" --body "$comment"
        log_success "Added comment to issue $issue_id"
    fi
    
    log_success "Updated issue $issue_id status to: $linear_state"
}

# Link PR to Linear issue
link_pr_to_issue() {
    local pr_url="$1"
    local issue_id="$2"
    local pr_title="$3"
    
    log_info "Linking PR to Linear issue $issue_id"
    
    local comment="🔗 **Pull Request Created**

**PR**: $pr_title
**URL**: $pr_url
**Status**: Ready for review

This PR was automatically created by Perpetual Worker to address this issue."

    linear comment create --issue "$issue_id" --body "$comment"
    log_success "Linked PR $pr_url to issue $issue_id"
}

# Sync Linear issues (main sync function)
sync_linear_issues() {
    log_info "Starting Linear sync process..."
    
    # Check prerequisites
    check_linear_cli
    
    # Fetch latest issues
    fetch_linear_issues
    
    # Convert to priorities format
    convert_linear_issues
    
    # Update project configuration to use Linear priorities
    if [ -f "$CONFIG_FILE" ]; then
        # Update priority_file setting to point to Linear priorities
        if grep -q "priority_file:" "$CONFIG_FILE"; then
            sed -i 's|priority_file:.*|priority_file: "LINEAR_PRIORITIES.md"|' "$CONFIG_FILE"
        fi
        log_success "Updated perpetual-config.yml to use Linear priorities"
    fi
    
    log_success "Linear sync completed successfully!"
}

# Setup Linear integration
setup_linear() {
    log_info "Setting up Linear integration..."
    
    # Install Linear CLI
    check_linear_cli
    
    # Create configuration
    init_linear_config
    
    # Create cache directory
    mkdir -p "$PROJECT_ROOT/cache/linear"
    
    # Add Linear sync to perpetual system
    if [ -f "$PROJECT_ROOT/prompts/primary-swarm.txt" ]; then
        # Check if Linear integration is already mentioned
        if ! grep -q "LINEAR_PRIORITIES.md" "$PROJECT_ROOT/prompts/primary-swarm.txt"; then
            cat >> "$PROJECT_ROOT/prompts/primary-swarm.txt" << 'EOF'

🔗 LINEAR INTEGRATION:
- Check LINEAR_PRIORITIES.md for current issue priorities from Linear
- Use Linear issue identifiers in commit messages (e.g., "feat: implement user auth (LIN-123)")
- Update issue status via comments when starting/completing work
- Link PRs to Linear issues automatically
EOF
            log_success "Added Linear integration to primary swarm prompt"
        fi
    fi
    
    log_success "Linear integration setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Set your Linear API key: export LINEAR_API_KEY='your-api-key'"
    echo "2. Edit config/linear-config.yml with your team details"
    echo "3. Run: ./scripts/linear-integration.sh sync"
    echo "4. Start perpetual worker: ./start-perpetual.sh"
}

# Create cron job for automatic syncing
setup_linear_cron() {
    log_info "Setting up automatic Linear sync..."
    
    local cron_command="*/5 * * * * cd '$PROJECT_ROOT' && '$SCRIPT_DIR/linear-integration.sh' sync >/dev/null 2>&1"
    
    # Add to existing crontab
    (crontab -l 2>/dev/null; echo "$cron_command") | crontab -
    
    log_success "Added Linear sync to crontab (every 5 minutes)"
}

# Main command dispatcher
case "${1:-help}" in
    "setup")
        setup_linear
        ;;
    "sync")
        sync_linear_issues
        ;;
    "fetch")
        fetch_linear_issues "${2:-50}"
        ;;
    "convert")
        convert_linear_issues
        ;;
    "update")
        if [ -z "$2" ] || [ -z "$3" ]; then
            log_error "Usage: $0 update <issue_id> <status> [comment]"
            exit 1
        fi
        update_linear_issue "$2" "$3" "$4"
        ;;
    "link")
        if [ -z "$2" ] || [ -z "$3" ] || [ -z "$4" ]; then
            log_error "Usage: $0 link <pr_url> <issue_id> <pr_title>"
            exit 1
        fi
        link_pr_to_issue "$2" "$3" "$4"
        ;;
    "cron")
        setup_linear_cron
        ;;
    "help"|*)
        echo "🔗 Linear Integration for Perpetual Worker"
        echo ""
        echo "Commands:"
        echo "  setup     - Initial Linear integration setup"
        echo "  sync      - Sync issues from Linear to priorities"
        echo "  fetch     - Fetch issues from Linear API"
        echo "  convert   - Convert cached issues to priorities format"
        echo "  update    - Update Linear issue status"
        echo "  link      - Link PR to Linear issue"
        echo "  cron      - Setup automatic syncing"
        echo "  help      - Show this help"
        echo ""
        echo "Environment variables:"
        echo "  LINEAR_API_KEY - Your Linear API key (required)"
        echo ""
        echo "Examples:"
        echo "  $0 setup"
        echo "  $0 sync"
        echo "  $0 update LIN-123 completed 'Fixed the authentication bug'"
        echo "  $0 link https://github.com/user/repo/pull/45 LIN-123 'Add user authentication'"
        ;;
esac