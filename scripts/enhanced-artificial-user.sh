#!/usr/bin/env bash
# Enhanced Artificial User - Handles ALL inputs including npm prompts

SESSION_NAME="${1:-mb-cc-v1}"
LOG_DIR="${2:-/home/<USER>/.midnight-burner/logs/cc-v1}"
mkdir -p "$LOG_DIR"

log_activity() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_DIR/artificial-user-enhanced.log"
}

# Response patterns for development
dev_responses=(
    "Continue with the next priority task"
    "Analyze current progress and implement next feature"
    "Work on the active development tasks systematically"
    "Check project status and proceed with development"
    "Keep building - focus on high priority items"
    "Good progress, continue with next phase"
    "Proceed with the implementation"
    "Continue development workflow"
    "Analyze and continue with next task"
    "Maintain development momentum"
)

get_random_response() {
    echo "${dev_responses[$RANDOM % ${#dev_responses[@]}]}"
}

log_activity "Enhanced artificial user started for $SESSION_NAME"

while true; do
    # Check each pane for prompts
    for pane in 0 1; do
        # Capture pane content
        content=$(tmux capture-pane -t "$SESSION_NAME:0.$pane" -p 2>/dev/null | tail -5)
        
        # Check for npm/yarn prompts
        if echo "$content" | grep -q "Ok to proceed? (y)"; then
            log_activity "Found npm prompt in pane $pane, sending 'y'"
            tmux send-keys -t "$SESSION_NAME:0.$pane" "y" C-m
            sleep 2
        fi
        
        # Check for other y/n prompts
        if echo "$content" | grep -qE "\(y/n\)|\[y/N\]|> y$"; then
            log_activity "Found y/n prompt in pane $pane, sending 'y'"
            tmux send-keys -t "$SESSION_NAME:0.$pane" "y" C-m
            sleep 2
        fi
        
        # Check for paused state (no recent activity)
        if echo "$content" | grep -qE "esc to interrupt|Bypassing Permissions" | head -1; then
            if [ $pane -eq 0 ]; then
                response=$(get_random_response)
                log_activity "Sending development response to pane $pane: $response"
                tmux send-keys -t "$SESSION_NAME:0.$pane" "$response" C-m
            fi
        fi
    done
    
    # Random interval 20-40 seconds
    sleep $((20 + RANDOM % 20))
done