#!/usr/bin/env bash
# Restart Midnight Burner System
# Gracefully restarts the perpetual development system

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}🔄 Midnight Burner - Restarting System${NC}"
    echo -e "${PURPLE}The AI that never sleeps${NC}"
    echo ""
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

# Function to find project path from session
find_project_from_session() {
    local session_name="$1"
    
    # Try to extract project path from tmux session environment
    if tmux has-session -t "$session_name" 2>/dev/null; then
        # Get the current directory of the first pane
        local pane_pwd=$(tmux display-message -p -t "$session_name:0.0" -F "#{pane_current_path}" 2>/dev/null || echo "")
        
        if [ -n "$pane_pwd" ] && [ -d "$pane_pwd" ]; then
            echo "$pane_pwd"
            return 0
        fi
    fi
    
    return 1
}

# Main execution
main() {
    print_header
    
    local project_path="$1"
    local session_name="${2:-midnight-burner}"
    
    # If no project path provided, try to find it from existing session
    if [ -z "$project_path" ]; then
        print_info "No project path specified, looking for existing sessions..."
        
        # Try to find from default session
        if project_path=$(find_project_from_session "$session_name"); then
            print_status "Found project: $project_path"
        else
            # Try to find any midnight-burner session
            while IFS= read -r session; do
                if [ -n "$session" ]; then
                    if project_path=$(find_project_from_session "$session"); then
                        session_name="$session"
                        print_status "Found project in session '$session': $project_path"
                        break
                    fi
                fi
            done < <(tmux list-sessions 2>/dev/null | grep -i midnight | cut -d: -f1)
        fi
        
        if [ -z "$project_path" ]; then
            echo -e "${YELLOW}Usage: $0 <project-path> [session-name]${NC}"
            echo ""
            echo "Examples:"
            echo "  $0 /path/to/your/project"
            echo "  $0 . my-project-midnight"
            echo ""
            echo "Could not determine project path from existing sessions."
            exit 1
        fi
    fi
    
    # Stop the system
    print_info "Stopping Midnight Burner..."
    "$SCRIPT_DIR/stop-midnight-burner.sh" "$session_name" || true
    
    # Wait for processes to fully terminate
    print_info "Waiting for processes to terminate..."
    sleep 3
    
    # Start the system again
    print_info "Starting Midnight Burner..."
    "$SCRIPT_DIR/start-midnight-burner.sh" "$project_path"
    
    print_status "Midnight Burner restarted successfully!"
}

# Run main function
main "$@"