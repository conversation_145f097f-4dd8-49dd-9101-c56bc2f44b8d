Got it. I’ll analyze the `midnight-burner` repository for the current implementation of your 24/7 coder orchestration, identifying bugs, performance issues, and design weaknesses. I’ll then provide <PERSON> <PERSON> <PERSON> with actionable recommendations for fixes and upgrades.

I’ll report back with detailed suggestions shortly.


# 📝 Current Orchestration Implementation

Midnight Burner’s orchestration is handled through Bash scripts and a **tmux-based multi-process setup**. When started, a `perpetual-orchestrator.sh` script launches a dedicated tmux session and splits it into four panes, each running a distinct agent role in parallel:

* **Primary Swarm (Pane 0):** The main development agent runs via the `claude-flow` CLI in a loop. It continuously processes the **primary prompt** (`primary-swarm.txt`) to implement features, fix bugs, and make commits. If the AI process exits (e.g. finishes or crashes), the script auto-restarts it after a short delay. This agent is configured never to stop on its own – the prompt explicitly instructs it to **“never exit”** and always ask for the next task.

* **Secondary Swarm (Pane 1):** A support/QA agent runs similarly via `claude-flow` using the **secondary prompt** (`secondary-swarm.txt`). It focuses on writing tests, documentation, reviewing code, etc., complementing the primary agent. This process also runs in a persistent loop so that if it terminates, it will restart automatically after a brief pause.

* **Artificial User (Pane 2):** A custom **user simulation script** (`artificial-user.sh`) runs to keep the AI agents active. This script periodically injects feedback or next-task prompts into the primary agent’s input, simulating an always-available user. It monitors the system’s state and, if it detects the AI agents have stopped responding (or one has crashed), it will issue a CTRL-C to terminate any remaining Claude processes and then relaunch both the primary and secondary swarms. Otherwise, on a regular interval it sends a random “encouragement” line (e.g. *“Continue with the next priority task”*) to prompt the next cycle. This ensures the primary swarm is never left waiting on user input and continuously moves to the next task.

* **Health Monitor (Pane 3):** A **monitoring script** (`health-monitor.sh`) runs continuously to track system metrics and agent status. It logs timestamps, counts the running Claude processes, checks memory usage, and reads load averages. These stats are logged to `health-monitor.log` and also printed in the tmux pane as a status dashboard. The monitor displays info like number of AI processes, current memory % used, CPU load, and confirms each of the four tmux panes/agents is active. This provides a real-time view of the “health” of the autonomous system.

All these components are orchestrated on a single machine without an external queue or distributed coordinator – **tmux acts as the process manager**. The design relies on shell scripts and the Claude tools: “Claude Code” (a CLI for code-generation) and **Claude Flow** (for orchestrating multi-step AI “flows”). The primary and secondary agents leverage Claude’s capabilities (via the `claude-flow@alpha` CLI) to read and modify the target repository. For example, the primary agent is expected to read files like README or TODOs, write code to implement features, run tests, commit changes, and even open pull requests using the Claude GitHub integration. The secondary agent similarly reads the repository state and produces supporting artifacts (tests, docs) in tandem.

**State and configuration:** The system creates a project-specific config at `.midnight-burner/config.yml` in the target repo on first run. There’s also a default `config/perpetual-config.yml` with global settings. These YAML configs define parameters such as project name/type, max agents, strategies, health check intervals, etc.. In practice, the current implementation uses some of these settings (e.g. session name, log directories), but many are **not yet dynamically applied** by the scripts (discussed below). The system does not utilize a traditional job queue; instead, task prioritization is handled implicitly by the AI (the primary agent decides what to do next based on the repository’s **priority file or TODOs** and open issues). Integration points for external tools exist – for example, there are scripts prepared for **Linear issue tracker integration** and for automated PR creation – but those are mostly stubs or not wired into the main loop yet.

Overall, the architecture is an “always-on” loop with **two AI coding agents** (primary & secondary) continuously cycling through analyze→code→commit phases, aided by an artificial user to simulate prompts, and overseen by a health monitor. This achieves a basic 24/7 autonomous coding workflow with the given repository. However, since this is an almost-working prototype, there are several bugs and incomplete features limiting its reliability and performance.

# ⚠️ Identified Bugs and Issues

**1. Platform-specific Commands:** The health monitor uses Linux-specific commands (`free`, `uptime`) to gather memory and load data. This will break on non-Linux systems (e.g. macOS has no `free` command). The install script attempts to support macOS/WSL generally, so this is an oversight. **Recommendation:** Make the health script cross-platform. For example, detect the OS and use appropriate alternatives (`vm_stat` or `memory_pressure` on macOS for memory, etc.), or use a Python snippet to fetch system metrics in a portable way. This ensures the monitor doesn’t crash or output blank data on unsupported platforms. **Test by** running the health monitor on macOS or WSL – it should report memory usage and load correctly (previously it would have failed or given no output). Updating this will preserve the “Production Ready” claim of cross-OS compatibility.

**2. Incomplete Process Cleanup on Stop:** The stop script uses `pkill` with a pattern to terminate Claude processes (`pkill -f "claude-flow.*perpetual"`). In practice, the actual process command for the AI agents may not include the word “perpetual”, so this pattern could miss them. (For instance, the primary swarm is launched via `npx claude-flow@alpha swarm "...prompt..." --claude` – unless “perpetual” appears in the prompt text or environment, the process name won’t match.) This means after stopping, some Claude background processes might continue running. **Recommendation:** Track the exact PIDs of the Claude processes when starting them, and kill those PIDs on stop. Alternatively, use a broader pattern (just `"claude-flow"` without the session filter) or store a marker in the process (like an env var or working directory) to reliably identify them. After implementing, **verify** by starting and stopping the system and then running `pgrep -f claude-flow` – there should be **0** matches (previously one could find leftover `claude-flow` processes). This fix prevents orphaned processes consuming resources after the system is supposed to be down.

**3. Config Values Not Enforced:** Several configuration options are defined in YAML but not actually used by the scripts, which can be considered a bug or oversight. For example, `max_agents` for primary/secondary swarms is set in config (default 5 and 3), but the orchestrator always launches exactly one process per swarm. Likewise, `pane_layout` (quad/horizontal/vertical) is in config but tmux splitting is hard-coded to a four-pane grid. This mismatch can confuse users who attempt to customize these settings. **Recommendation:** Parse and apply the config. At minimum, respect the `pane_layout` setting (e.g. if `horizontal`, split tmux into two wide panes rather than four). If `max_agents > 1`, consider launching additional agent processes or threads – or document that multi-agent parallelism is not yet available. Until fully supported, the config or docs should clarify this. **Test by** setting these values (e.g. `max_agents: 2` or `pane_layout: "horizontal"`) and running the start script – ensure the tmux session reflects the intended layout or agent count, or at least prints a warning if not supported. This alignment between config and behavior will prevent user confusion and potential misconfiguration.

**4. Lack of Rate Limiting on Restarts:** The artificial user’s recovery logic can cause rapid restarts in case of persistent failures. It checks if `process_count < 2` and then immediately kills and restarts both swarms. If the Claude processes keep crashing (e.g. due to an invalid model key or a fatal error in prompts), this will loop very quickly. There is a `max_restart_attempts` field in config for monitoring, but it’s not used in the script. **Recommendation:** Implement a backoff or respect `max_restart_attempts`. For instance, count restart cycles and after N retries in a short span, stop trying or increase the sleep interval to avoid thrashing. Also log an error when giving up. **Test scenario:** Induce a failure (e.g. use an invalid API key or intentionally crash the Claude process) and observe the behavior – it should attempt a few restarts and then halt or wait, rather than endlessly restarting multiple times per minute. This will protect against infinite loops that could hammer the API or system resources.

**5. Minor User Experience Bugs:** There are a few minor issues affecting usability. For example, if one runs the main `midnight-burner.sh` without running `install.sh` first, the core scripts might not exist (since they are generated during install). The test suite expects those scripts to be present, but a new user might skip straight to `./midnight-burner.sh`. **Recommendation:** The main script could detect this and either auto-run the installer or output a reminder. Another example: the restart script tries to infer the project path from an existing session by querying tmux pane 0’s path – if that fails, it prints a usage message but doesn’t explicitly error out, which could be confusing. Ensuring clear messages (or integrating the restart logic into the main script to avoid needing a separate command) would help. **Test by** simulating these scenarios (run without install, or restart with no session) and making sure the system responds with helpful guidance rather than failing silently.

# 🐎 Performance Bottlenecks and Optimization

**1. Resource Usage with Parallel Agents:** Running multiple large-model agents concurrently is **resource intensive**. The documentation notes 8–13 processes and \~2–4 GB memory usage in typical operation. In the current implementation, the primary and secondary Claude processes run in parallel continuously. If the config’s plan for up to 5 primary agents were realized, that’d multiply resource consumption. On a single server, CPU contention or hitting RAM limits could significantly slow down each agent’s response times (or trigger OOM issues). **Recommendation:** Introduce a mechanism to scale the concurrency based on available resources. For example, start with one primary and one secondary agent, and only spawn additional agents if the system has idle CPU or during off-peak times. Alternatively, implement **time-slicing**: let the primary agent run a cycle while secondary is idle or in analysis, then alternate – effectively serializing some work to reduce peak load. This could be coordinated via the artificial user or a simple scheduler loop. Another approach is to allow agents to run on separate machines (not currently supported, but could be considered for the future using a lightweight message queue or by containerizing each agent). **Test** this by monitoring system metrics: with optimization, the CPU and memory usage should stay below a safe threshold (e.g. CPU < 100% on a 4-core system, memory < 80%) even as the system runs for hours. If adding a dynamic scaler, verify that launching or stopping extra agents based on load works as expected.

**2. Inefficient Re-analysis of Project State:** Each new development cycle likely prompts the AI to read a lot of context (project files, docs, etc.) from scratch. The primary prompt explicitly tells the agent to *“analyze project state and priorities”* at the start of each cycle. In a large repository, this could mean reading lengthy README/PRIORITIES files or scanning code repeatedly. This not only costs time each cycle but also uses up token/context window budget for the model. **Recommendation:** Implement a smarter context management. For instance, maintain a summary of the project or a list of “known todos” so the AI doesn’t need to read everything every time. One idea is to use a **caching layer**: on first run, have the agent (or a setup script) generate a structured summary of the codebase and open issues. Then at each cycle, feed the summary plus only the sections that have changed or are relevant to the current task. Another approach is using embeddings: vectorize the codebase and let the AI query it as needed (this would be a significant enhancement beyond the current design). Even simpler, limit the agent’s “full analysis” cycles to maybe once every N iterations or when a big change (like a new issue) is detected, instead of every single loop. **Testing:** You can create a dummy large project (hundreds of files) and measure cycle time or Claude API usage before and after such caching. The optimized approach should result in faster cycle completion and fewer tokens consumed per iteration, without missing important context.

**3. Potential Logging Overhead:** The health monitor writes to its log file on every check (every 60s by default), and the artificial user logs every time it sends a prompt or restarts something. Over long periods (days/weeks), these log files can grow large and possibly impact disk I/O. The config suggests a `log_retention_days` setting, but log rotation/deletion isn’t implemented. While this isn’t an immediate performance hit, it could become one (low disk space or slower file writes). **Recommendation:** Implement a simple log rotation strategy – e.g., compress or archive logs older than X days, or cap the file size and rotate. This can be done via a cron job or within the health monitor loop (every few checks, trim the file). Also consider reducing log verbosity once things are stable (for example, the monitor could log only on significant changes or warnings, rather than every minute). **Test** by letting the system run in a long-lived scenario and checking that the log files do not grow without bound (and that recent logs are still accessible). Also verify that log trimming doesn’t break any tail-following that the status script or user might be doing.

**4. Claude API Throughput and Rate Limits:** Running two Claude agents 24/7 means a *lot* of API calls or compute cycles. If using a paid API, this might hit rate limits or incur high costs; if using a local Claude instance (if such exists in the future), it may saturate the model. A performance consideration is ensuring the AI isn’t asked to regenerate responses too frequently or concurrently beyond what the API allows. The current design does restart agents immediately on failure and prompts continuously. **Recommendation:** Implement a brief cooldown between cycles or when errors occur. For instance, if a cycle completes extremely quickly, insert a short `sleep` before the next one, to mimic a human developer’s pace and avoid hammering the API. Also use the `health-monitor` to watch for any error patterns – if the API starts rejecting calls, log a warning and slow down the cycle frequency. **Testing:** Run the system under a rate-limit scenario (maybe configure Claude with a small quota or use a dummy endpoint that allows only N requests per minute) and confirm that the system backs off appropriately instead of spamming and failing consistently. This ensures the “continuous” development doesn’t translate into “continuous error spam” under real-world API constraints.

# 🏗️ Architectural Issues and Improvements

**1. Coordination Between Agents (Concurrency Control):** Currently, the primary and secondary swarms operate mostly independently – there’s no explicit synchronization of their actions. This could lead to **race conditions**. For example, the primary might be in the middle of editing a file when the secondary decides to run tests or linting on it, or both agents might attempt to `git commit` around the same time. Without coordination, one agent’s changes could conflict with the other’s. The design intent is that the secondary “monitors” the primary and provides support, but in practice both are just reading the shared filesystem and acting on their own prompts. **Improvement:** Introduce a simple workflow manager to mediate between the two swarms. One approach is to implement phases: e.g., have the primary agent signal (perhaps by creating a file or writing a log line) when it’s finished a coding task, which the secondary script can detect and then run a testing/documentation cycle, and vice versa. This could be orchestrated by the artificial user process – it can alternate its prompts between primary and secondary or enable/disable one of the panes. A lighter approach could be adding instructions in the secondary prompt to always pull the latest code and only run after new commits (the AI might already infer this, but making it explicit helps). **Test** this coordination by looking at commit history and outputs: after changes, the secondary agent should respond (e.g. writing tests) and not step on the primary’s toes. In a scenario where both try to commit, the improvement would ensure one waits or integrates changes from the other (for instance, using `git pull` and merge if needed). This change will make the dual-swarm setup much more robust during real simultaneous development.

**2. Unused “Max Agents” and Distributed Mode:** The config structure hints at scaling beyond one primary and one secondary agent (with strategies like “centralized” vs “distributed”), but the current orchestrator doesn’t implement multiple agents per swarm. This is an architectural placeholder – potentially to have a *swarm* of agents solving different tasks in parallel – but without a task queue or partitioning of work, simply launching 5 identical primary agents would cause chaos (or duplicate efforts). **Improvement:** If the goal is to utilize multiple agents, an **agent scheduler** or queue should be introduced. For example, one could integrate a job queue where tasks (like “Implement feature X” or “Refactor module Y”) are queued up, and multiple agent processes pull tasks from this queue. This requires more complex coordination logic (perhaps using a small database or in-memory queue). In the near term, if parallelism is not truly needed, it may be better to restrict `max_agents` to 1 and clearly document that. If it is needed, implement a master controller (could be a Node.js script or extend the bash scripts) that reads a list of tasks (maybe from a `PRIORITIES.md` or Linear issues) and assigns them to agents in separate tmux panes. **Testing:** To verify, one could simulate two agents by giving them different specific tasks (e.g., Agent A works on a feature while Agent B writes a doc) and ensure they don’t conflict and that both complete their tasks. This is a significant design extension – it should be approached once the single-agent-per-role path is stable. For now, clarifying or disabling the unused multi-agent settings might be the safest course to avoid misconfiguration.

**3. Reliance on tmux for Process Management:** Using tmux is a clever way to allow user visibility and keep processes running, but it is somewhat ad-hoc as a long-term orchestration solution. There’s no centralized error handling or insight into what each agent is doing beyond what’s visible in their pane. If one process unexpectedly exits, tmux will keep the session alive (and the artificial user might restart it), but if something hangs (e.g., an agent stuck in a long loop), the system might not detect that as “failure.” **Improvement:** Consider building a more direct orchestration layer in code. For example, a Node.js master script could spawn the agent processes (using `child_process` or a task runner) and communicate with them via IPC or websockets. This would allow better tracking of each agent’s status and more granular control than sending keys to tmux. Another alternative is leveraging the **Claude Flow** programmatically: since `claude-flow` is included as a dependency, there might be a way to invoke flows via an API instead of CLI, which could give structured callbacks when an agent finishes a task. While tmux provides a quick visual, it’s not optimal for headless or production environments (and as an example, if the server reboots unexpectedly, tmux won’t start on its own unless wrapped in a service). In the short term, this may be acceptable, but for robustness, migrating to a more standard process manager (systemd service, or a persistent Node/PM2 process managing the agents) would be beneficial. **Testing:** After such a refactor, one would test by deliberately causing agent failures and ensuring the master process catches it and restarts as intended, and by running the system without a user session (headless) to see that it still functions and logs properly. This would lay groundwork for scaling the system or running it as a true service.

**4. Linear Integration Not Yet Integrated:** There is support for Linear (project management) in theory – e.g., a `linear-mcp-integration.sh` to set up a Linear MCP server, and a `linear-integration.sh` that can fetch issues and create a `LINEAR_PRIORITIES.md` file from them. However, these are **not invoked** anywhere in the main orchestration. As a result, the system currently isn’t actually using any external work queue or issue tracker, despite marketing “Linear integration” as a feature. **Improvement:** Finish hooking up Linear integration. There are two possible modes: (a) **Via MCP (Model Context Protocol)** – which would let the Claude agent query and update Linear issues through tool use (the prompt could include commands like `linear_search_issues` etc., as seen in the MCP integration script). To enable this, after installation the orchestrator should call `linear-mcp-integration.sh` to start the MCP server and merge the Linear tool instructions into the primary prompt. (b) **Via periodic syncing** – using the Linear CLI. For example, the health monitor or a separate thread could run `linear-integration.sh` every 5 minutes (per config sync\_interval) to pull the latest issues and update a local priorities file that the AI reads. Either approach (or both) should be behind a config flag (like `integration.linear.enabled`). Once implemented, the primary swarm will genuinely prioritize real issues: it could pick the top “urgent” Linear issue to work on first, automatically mark it in progress, and later mark it done – fulfilling the intended workflow. **Test** by connecting to a Linear test workspace: the AI should start picking up issues (the logs or its messages should show it found an issue via `linear_search_issues` if using MCP, or it read the `LINEAR_PRIORITIES.md` file if using the CLI sync method). Also test that issue status changes propagate (e.g., when the AI “completes” an issue, the issue in Linear is moved to Done). This will close a key feature gap noted in the project’s plan.

**5. Automated PR Creation Workflow:** The current system relies on Claude’s GitHub integration to create pull requests, but there is no guarantee the AI will actually open PRs at the configured intervals. The config and prompts indicate that every \~4 hours or \~3-8 file changes, a PR should be created, and a `PR_NOTES.md` included, but this is left to the AI’s initiative. In an “almost-working” state, this might be inconsistent (the AI could keep committing to a single branch indefinitely if not properly prompted). **Improvement:** Implement a more deterministic PR trigger. One approach is to utilize the **Claude Code GitHub App** capabilities directly: for example, send a `/pr` command to Claude after a certain time or number of commits. Another approach is outside the AI – e.g., a small script that runs via cron every 4 hours to check if the branch has un-PR’d commits and, if so, uses the GitHub API to open a PR (and perhaps ask the AI to write the summary). A simpler integrated solution is to include a timer in the artificial user or health monitor: it could count cycles or wall-clock time and then instruct the AI like *“Please open a pull request now for review of recent changes”*. This explicit instruction would cause Claude to bundle changes into a PR. **Test** by letting the system run through multiple cycles and verifying that PRs indeed show up in the repo at the expected cadence (with appropriate titles and PR\_NOTES content). After implementing, the “PR Generation” feature advertisedwill be reliable. It’s also important to test edge cases – e.g., if there were no significant changes in the interval, ensure it doesn’t open empty PRs, or if the branch already has an open PR, perhaps just update it instead of opening duplicates.

**6. Persistence of AI State Across Restarts:** As it stands, if the system or server restarts, the tmux session and AI processes will be gone. While the code will come back up (especially if a cronjob or other auto-start is put in place), the AI will lose its conversational history. It will re-read the repository and continue, but any in-memory context (like why it made a certain change) is lost. This is somewhat mitigated by the fact that the important state is in the code and text files (so the AI can derive context from them), but it might lead to repetitive work or lost continuity for long-running tasks. **Improvement:** Implement a basic memory or state save. For example, periodically save the conversation or a summary of recent AI thoughts to a state file (perhaps in the `state/` directory). On restart, load this and prepend it to the prompt (or feed it as “previous context”). Even a simple journal of last completed tasks could help. Additionally, enabling the **auto-save of Claude conversation** (if Claude Flow has such a feature to resume contexts) would be valuable. Another angle is to use the `cronjob: true` config to set up a system service that launches the whole system on boot – ensuring it *does* come back after a reboot (currently, `cronjob: true` is in config but the installer doesn’t actually install a cron entry to use it). That part should be implemented: e.g., have `install.sh` prompt to add `@reboot /path/to/start-perpetual.sh` to crontab if cronjob is enabled. **Test** by simulating a reboot: stop the system, clear its in-memory context, then start it again, and see if it picks up work without duplicating efforts. With a saved state, the primary agent might, for instance, recall that it was midway through implementing a certain feature and continue, whereas previously it might inadvertently start a different task after a restart.

In summary, addressing these architectural and functional issues will take the system from an “almost-working” prototype to a much more **robust 24/7 coder**. Each recommendation above is written with implementation in mind – a coding assistant like Claude or Codex could use them as action items (e.g. “modify this script to add OS detection for memory stats” or “add a loop in artificial-user.sh to pause prompting secondary during primary commits”) and proceed to code the changes. After making these changes, thorough testing should be done: not only unit tests of scripts but also live integration tests with a sample repository over an extended period. Verify that the autonomous developer runs smoothly, adapts to project inputs (issues/requests), and handles errors gracefully. With these fixes and enhancements, Midnight Burner can evolve from a promising alpha into a reliable continuous development orchestrator.&#x20;
