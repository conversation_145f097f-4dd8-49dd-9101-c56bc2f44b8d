#!/usr/bin/env bash
# Artificial User Agent - Keeps swarms active with intelligent responses

LOG_DIR="${1:-logs}"
SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

mkdir -p "$LOG_DIR"

log_activity() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_DIR/artificial-user.log"
}

# Response patterns
responses=(
    "Continue with the next priority task"
    "Analyze current progress and implement next feature"
    "Work on the active development tasks systematically"
    "Check project status and proceed with development"
    "Keep building - focus on high priority items"
    "Good progress, continue with next phase"
    "Proceed with the implementation"
    "Continue development workflow"
    "Analyze and continue with next task"
    "Maintain development momentum"
)

get_random_response() {
    echo "${responses[$RANDOM % ${#responses[@]}]}"
}

log_activity "Artificial user agent started"

while true; do
    # Check if swarms need interaction
    sleep $((30 + RANDOM % 30))  # 30-60 second intervals
    
    # Check process health
    process_count=$(pgrep -f "claude-flow" | wc -l)
    
    if [ "$process_count" -lt 2 ]; then
        log_activity "Low process count ($process_count), restarting swarms..."
        tmux send-keys -t "$SESSION_NAME:0.0" C-c
        tmux send-keys -t "$SESSION_NAME:0.1" C-c
        sleep 2
        tmux send-keys -t "$SESSION_NAME:0.0" "npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/primary-swarm.txt')\" --claude" C-m
        tmux send-keys -t "$SESSION_NAME:0.1" "npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/secondary-swarm.txt')\" --claude" C-m
    else
        # Check for npm prompts first
        for pane in 0 1; do
            content=$(tmux capture-pane -t "$SESSION_NAME:0.$pane" -p 2>/dev/null | tail -5)
            if echo "$content" | grep -q "Ok to proceed? (y)"; then
                log_activity "Found npm prompt in pane $pane, sending 'y'"
                tmux send-keys -t "$SESSION_NAME:0.$pane" "y" C-m
            fi
        done
        
        # Send encouraging response to primary swarm
        response=$(get_random_response)
        log_activity "Sending response: $response"
        tmux send-keys -t "$SESSION_NAME:0.0" "$response" C-m
    fi
done
