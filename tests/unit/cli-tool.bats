#!/usr/bin/env bats

# Unit tests for midnight-burner-cli.sh

setup() {
  # Set up test environment
  export MIDNIGHT_BURNER_ROOT="/home/<USER>/github/midnight-burner"
  export CLI_TOOL="$MIDNIGHT_BURNER_ROOT/midnight-burner-cli.sh"
  export TEST_MODE=1
  export TEST_PROJECT_DIR="/tmp/test-project-$$"
  
  # Create test project directory
  mkdir -p "$TEST_PROJECT_DIR"
}

teardown() {
  # Clean up test environment
  rm -rf "$TEST_PROJECT_DIR"
  unset TEST_MODE
  unset TEST_PROJECT_DIR
}

@test "CLI tool exists and is executable" {
  [ -f "$CLI_TOOL" ]
  [ -x "$CLI_TOOL" ]
}

@test "CLI displays help message" {
  run bash "$CLI_TOOL" --help
  [ "$status" -eq 0 ]
  [[ "$output" =~ "Midnight Burner CLI" ]]
  [[ "$output" =~ "Usage:" ]]
}

@test "CLI displays version information" {
  run bash "$CLI_TOOL" --version
  [ "$status" -eq 0 ]
  [[ "$output" =~ "version" ]] || [[ "$output" =~ "1.0" ]]
}
