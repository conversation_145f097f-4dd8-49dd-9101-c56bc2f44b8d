#!/usr/bin/env bash
# Midnight Burner CLI - Safe project management tool

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
MIDNIGHT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$HOME/.midnight-burner"
PROJECTS_FILE="$CONFIG_DIR/projects.json"
LOGS_DIR="$CONFIG_DIR/logs"
STATE_DIR="$CONFIG_DIR/state"

# Ensure config directory exists
mkdir -p "$CONFIG_DIR" "$LOGS_DIR" "$STATE_DIR"

# Check for jq
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}Warning: jq is not installed. Using simple file storage.${NC}"
    USE_SIMPLE_STORAGE=true
else
    USE_SIMPLE_STORAGE=false
fi

# Initialize projects file if it doesn't exist
if [[ ! -f "$PROJECTS_FILE" ]]; then
    if [[ "$USE_SIMPLE_STORAGE" == "true" ]]; then
        touch "$PROJECTS_FILE"
    else
        echo '{}' > "$PROJECTS_FILE"
    fi
fi

print_banner() {
    echo -e "${PURPLE}"
    echo "🔥 Midnight Burner CLI"
    echo "   The AI that never sleeps"
    echo -e "${NC}"
}

print_help() {
    echo "Usage: midnight-burner <command> [options]"
    echo ""
    echo "Commands:"
    echo "  init <path>          Initialize a new project for autonomous development"
    echo "  start <project>      Start development on a project"
    echo "  stop <project>       Stop development on a project"
    echo "  status               Show status of all projects"
    echo "  list                 List all configured projects"
    echo "  remove <project>     Remove a project configuration"
    echo "  logs <project>       Show logs for a project"
    echo "  attach <project>     Attach to a running project's tmux session"
    echo ""
    echo "Examples:"
    echo "  midnight-burner init /path/to/project"
    echo "  midnight-burner start my-project"
    echo "  midnight-burner status"
}

# Get project info from JSON
get_project() {
    local name="$1"
    jq -r ".\"$name\"" "$PROJECTS_FILE" 2>/dev/null
}

# Save project info to JSON
save_project() {
    local name="$1"
    local path="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create project entry
    local project_json=$(jq -n \
        --arg path "$path" \
        --arg created "$timestamp" \
        --arg status "configured" \
        '{path: $path, created: $created, status: $status}')
    
    # Update projects file
    jq ".\"$name\" = $project_json" "$PROJECTS_FILE" > "$PROJECTS_FILE.tmp" && \
    mv "$PROJECTS_FILE.tmp" "$PROJECTS_FILE"
}

# Update project status
update_status() {
    local name="$1"
    local status="$2"
    
    jq ".\"$name\".status = \"$status\"" "$PROJECTS_FILE" > "$PROJECTS_FILE.tmp" && \
    mv "$PROJECTS_FILE.tmp" "$PROJECTS_FILE"
}

# Initialize a new project
cmd_init() {
    local project_path="$1"
    
    if [[ -z "$project_path" ]]; then
        echo -e "${RED}Error: Please provide a project path${NC}"
        echo "Usage: midnight-burner init <path>"
        exit 1
    fi
    
    # Convert to absolute path
    project_path=$(cd "$project_path" 2>/dev/null && pwd) || {
        echo -e "${RED}Error: Directory '$project_path' does not exist${NC}"
        exit 1
    }
    
    # Extract project name from path
    local project_name=$(basename "$project_path")
    
    # Check if already configured
    if [[ $(get_project "$project_name") != "null" ]]; then
        echo -e "${YELLOW}Warning: Project '$project_name' already configured${NC}"
        echo "Path: $(get_project "$project_name" | jq -r .path)"
        return
    fi
    
    echo -e "${BLUE}Initializing project: $project_name${NC}"
    echo "Path: $project_path"
    
    # Create project-specific directories
    mkdir -p "$LOGS_DIR/$project_name"
    mkdir -p "$STATE_DIR/$project_name"
    
    # Save project configuration
    save_project "$project_name" "$project_path"
    
    # Create instruction files if they don't exist
    if [[ ! -f "$project_path/AGENTS.md" ]]; then
        echo -e "${YELLOW}Creating AGENTS.md...${NC}"
        cat > "$project_path/AGENTS.md" << 'EOF'
# Midnight Burner Instructions

## Your Mission
Work continuously on this project, creating PRs every 4 hours.

## Priorities
1. Check TODO.md for immediate tasks
2. Look for TODO/FIXME comments in code
3. Review issues (if available)
4. Improve code quality and tests

## Workflow
1. Analyze current state
2. Choose highest priority task
3. Implement with tests
4. Create PR
5. Loop forever

Remember: You NEVER stop working!
EOF
    fi
    
    if [[ ! -f "$project_path/TODO.md" ]]; then
        echo -e "${YELLOW}Creating TODO.md...${NC}"
        echo "# TODO List" > "$project_path/TODO.md"
        echo "" >> "$project_path/TODO.md"
        echo "- [ ] Add your tasks here" >> "$project_path/TODO.md"
    fi
    
    echo -e "${GREEN}✅ Project initialized successfully!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Edit $project_path/AGENTS.md to customize instructions"
    echo "2. Update $project_path/TODO.md with tasks"
    echo "3. Run: midnight-burner start $project_name"
}

# Start development on a project
cmd_start() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide a project name${NC}"
        echo "Usage: midnight-burner start <project>"
        exit 1
    fi
    
    # Get project info
    local project_info=$(get_project "$project_name")
    if [[ "$project_info" == "null" ]]; then
        echo -e "${RED}Error: Project '$project_name' not found${NC}"
        echo "Run 'midnight-burner list' to see configured projects"
        exit 1
    fi
    
    local project_path=$(echo "$project_info" | jq -r .path)
    local session_name="mb-$project_name"
    
    # Check if already running
    if tmux has-session -t "$session_name" 2>/dev/null; then
        echo -e "${YELLOW}Project '$project_name' is already running${NC}"
        echo "Use 'midnight-burner attach $project_name' to view"
        exit 0
    fi
    
    echo -e "${BLUE}Starting Midnight Burner for: $project_name${NC}"
    echo "🔒 Working directory: $project_path"
    echo ""
    
    # Create tmux session IN THE PROJECT DIRECTORY
    tmux new-session -d -s "$session_name" -c "$project_path"
    
    # Split into 4 panes (all in project directory)
    tmux split-window -h -t "$session_name" -c "$project_path"
    tmux split-window -v -t "$session_name:0.0" -c "$project_path"
    tmux split-window -v -t "$session_name:0.1" -c "$project_path"
    
    # Start primary swarm (LOCKED to project directory)
    tmux send-keys -t "$session_name:0.0" "echo '🔥 Primary swarm starting in: $project_path' && while true; do npx claude-flow@alpha swarm \"\$(cat '$MIDNIGHT_DIR/prompts/primary-swarm.txt')\" --claude || echo 'Restarting...'; sleep 5; done" C-m
    
    # Start secondary swarm (LOCKED to project directory)
    tmux send-keys -t "$session_name:0.1" "echo '🛡️ Secondary swarm starting in: $project_path' && while true; do npx claude-flow@alpha swarm \"\$(cat '$MIDNIGHT_DIR/prompts/secondary-swarm.txt')\" --claude || echo 'Restarting...'; sleep 5; done" C-m
    
    # Artificial user (in midnight-burner dir for safety)
    tmux send-keys -t "$session_name:0.2" "cd '$MIDNIGHT_DIR' && '$MIDNIGHT_DIR/scripts/artificial-user.sh' '$LOGS_DIR/$project_name'" C-m
    
    # Health monitor (in midnight-burner dir for safety)
    tmux send-keys -t "$session_name:0.3" "cd '$MIDNIGHT_DIR' && '$MIDNIGHT_DIR/scripts/health-monitor.sh' '$LOGS_DIR/$project_name'" C-m
    
    # Update status
    update_status "$project_name" "running"
    
    echo -e "${GREEN}✅ Midnight Burner started successfully!${NC}"
    echo ""
    echo "Commands:"
    echo "  View:   midnight-burner attach $project_name"
    echo "  Stop:   midnight-burner stop $project_name"
    echo "  Logs:   midnight-burner logs $project_name"
    echo ""
    echo -e "${YELLOW}⚠️  IMPORTANT: Swarms are locked to project directory${NC}"
    echo "They can only modify files in: $project_path"
}

# Stop development on a project
cmd_stop() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide a project name${NC}"
        exit 1
    fi
    
    local session_name="mb-$project_name"
    
    if ! tmux has-session -t "$session_name" 2>/dev/null; then
        echo -e "${YELLOW}Project '$project_name' is not running${NC}"
        exit 0
    fi
    
    echo -e "${BLUE}Stopping Midnight Burner for: $project_name${NC}"
    tmux kill-session -t "$session_name"
    
    # Update status
    update_status "$project_name" "stopped"
    
    echo -e "${GREEN}✅ Project stopped${NC}"
}

# Show status of all projects
cmd_status() {
    echo -e "${BLUE}Midnight Burner Projects Status${NC}"
    echo ""
    
    if [[ ! -s "$PROJECTS_FILE" ]] || [[ $(jq '. | length' "$PROJECTS_FILE") -eq 0 ]]; then
        echo "No projects configured"
        echo "Run 'midnight-burner init <path>' to add a project"
        return
    fi
    
    # Header
    printf "%-20s %-10s %-50s\n" "PROJECT" "STATUS" "PATH"
    printf "%-20s %-10s %-50s\n" "-------" "------" "----"
    
    # List projects
    jq -r 'to_entries[] | [.key, .value.status, .value.path] | @tsv' "$PROJECTS_FILE" | \
    while IFS=$'\t' read -r name status path; do
        # Check if tmux session exists
        session_name="mb-$name"
        if tmux has-session -t "$session_name" 2>/dev/null; then
            status="${GREEN}running${NC}"
        else
            status="${YELLOW}stopped${NC}"
        fi
        
        printf "%-20s %-20b %-50s\n" "$name" "$status" "$path"
    done
}

# List all configured projects
cmd_list() {
    echo -e "${BLUE}Configured Projects${NC}"
    echo ""
    
    if [[ ! -s "$PROJECTS_FILE" ]] || [[ $(jq '. | length' "$PROJECTS_FILE") -eq 0 ]]; then
        echo "No projects configured"
        return
    fi
    
    jq -r 'to_entries[] | "• \(.key) - \(.value.path)"' "$PROJECTS_FILE"
}

# Remove a project configuration
cmd_remove() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide a project name${NC}"
        exit 1
    fi
    
    # Check if running
    local session_name="mb-$project_name"
    if tmux has-session -t "$session_name" 2>/dev/null; then
        echo -e "${RED}Error: Project is currently running${NC}"
        echo "Stop it first: midnight-burner stop $project_name"
        exit 1
    fi
    
    # Remove from config
    jq "del(.\"$project_name\")" "$PROJECTS_FILE" > "$PROJECTS_FILE.tmp" && \
    mv "$PROJECTS_FILE.tmp" "$PROJECTS_FILE"
    
    echo -e "${GREEN}✅ Project '$project_name' removed${NC}"
}

# Show logs for a project
cmd_logs() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide a project name${NC}"
        exit 1
    fi
    
    local log_dir="$LOGS_DIR/$project_name"
    
    if [[ ! -d "$log_dir" ]]; then
        echo -e "${YELLOW}No logs found for project '$project_name'${NC}"
        exit 0
    fi
    
    # Show recent logs
    echo -e "${BLUE}Recent logs for: $project_name${NC}"
    echo ""
    
    if [[ -f "$log_dir/orchestrator.log" ]]; then
        tail -n 50 "$log_dir/orchestrator.log"
    else
        echo "No orchestrator logs found"
    fi
}

# Attach to a running project
cmd_attach() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide a project name${NC}"
        exit 1
    fi
    
    local session_name="mb-$project_name"
    
    if ! tmux has-session -t "$session_name" 2>/dev/null; then
        echo -e "${RED}Error: Project '$project_name' is not running${NC}"
        echo "Start it first: midnight-burner start $project_name"
        exit 1
    fi
    
    echo -e "${BLUE}Attaching to: $project_name${NC}"
    echo "Detach with: Ctrl+B, D"
    sleep 1
    
    tmux attach -t "$session_name"
}

# Main command dispatcher
main() {
    if [[ $# -eq 0 ]]; then
        print_banner
        print_help
        exit 0
    fi
    
    local command="$1"
    shift
    
    case "$command" in
        init)
            cmd_init "$@"
            ;;
        start)
            cmd_start "$@"
            ;;
        stop)
            cmd_stop "$@"
            ;;
        status)
            cmd_status "$@"
            ;;
        list)
            cmd_list "$@"
            ;;
        remove)
            cmd_remove "$@"
            ;;
        logs)
            cmd_logs "$@"
            ;;
        attach)
            cmd_attach "$@"
            ;;
        help|--help|-h)
            print_banner
            print_help
            ;;
        *)
            echo -e "${RED}Error: Unknown command '$command'${NC}"
            echo ""
            print_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"