#!/usr/bin/env bash
# Midnight Burner Test Suite
# Comprehensive tests for the perpetual development system

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="$SCRIPT_DIR/test-workspace"
TEST_PROJECT="$TEST_DIR/test-project"
TEST_SESSION="test-midnight-burner"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Print functions
print_test_header() {
    echo -e "${PURPLE}🧪 Midnight Burner Test Suite${NC}"
    echo -e "${PURPLE}Testing the AI that never sleeps${NC}"
    echo ""
}

print_test() {
    echo -e "${BLUE}TEST:${NC} $1"
    TESTS_RUN=$((TESTS_RUN + 1))
}

print_pass() {
    echo -e "${GREEN}  ✅ PASS:${NC} $1"
    TESTS_PASSED=$((TESTS_PASSED + 1))
}

print_fail() {
    echo -e "${RED}  ❌ FAIL:${NC} $1"
    TESTS_FAILED=$((TESTS_FAILED + 1))
}

print_info() {
    echo -e "${BLUE}  ℹ️${NC} $1"
}

print_summary() {
    echo ""
    echo -e "${PURPLE}Test Summary${NC}"
    echo "============="
    echo -e "Total Tests: $TESTS_RUN"
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        return 0
    else
        echo -e "${RED}❌ Some tests failed${NC}"
        return 1
    fi
}

# Cleanup function
cleanup() {
    print_info "Cleaning up test environment..."
    
    # Stop any test sessions
    "$SCRIPT_DIR/stop-midnight-burner.sh" "$TEST_SESSION" 2>/dev/null || true
    
    # Remove test directory
    rm -rf "$TEST_DIR"
    
    print_info "Cleanup completed"
}

# Setup test environment
setup_test_env() {
    print_info "Setting up test environment..."
    
    # Create test directory
    mkdir -p "$TEST_PROJECT"
    
    # Create a minimal test project
    cat > "$TEST_PROJECT/README.md" << 'EOF'
# Test Project

This is a test project for Midnight Burner.

## TODO
- [ ] Implement basic functionality
- [ ] Add tests
- [ ] Write documentation
- [ ] Test MCP integration
- [ ] Test Linear integration
EOF

    cat > "$TEST_PROJECT/package.json" << 'EOF'
{
  "name": "test-project",
  "version": "1.0.0",
  "description": "Test project for Midnight Burner",
  "main": "index.js",
  "scripts": {
    "test": "echo 'No tests yet'",
    "lint": "echo 'No linting yet'"
  }
}
EOF

    # Create a simple source file
    mkdir -p "$TEST_PROJECT/src"
    cat > "$TEST_PROJECT/src/index.js" << 'EOF'
// Test project main file
console.log('Hello from test project');
EOF

    print_info "Test environment ready"
}

# Test 1: Check prerequisites
test_prerequisites() {
    print_test "Checking prerequisites"
    
    local has_node=$(command -v node >/dev/null 2>&1 && echo "yes" || echo "no")
    local has_git=$(command -v git >/dev/null 2>&1 && echo "yes" || echo "no")
    local has_tmux=$(command -v tmux >/dev/null 2>&1 && echo "yes" || echo "no")
    
    if [ "$has_node" = "yes" ] && [ "$has_git" = "yes" ] && [ "$has_tmux" = "yes" ]; then
        print_pass "All prerequisites installed"
    else
        print_fail "Missing prerequisites - Node: $has_node, Git: $has_git, tmux: $has_tmux"
    fi
}

# Test 2: Check script files exist
test_script_files() {
    print_test "Checking script files"
    
    local missing_files=0
    local scripts=(
        "midnight-burner.sh"
        "install.sh"
        "start-midnight-burner.sh"
        "stop-midnight-burner.sh"
        "restart-midnight-burner.sh"
        "scripts/perpetual-orchestrator.sh"
        "scripts/artificial-user.sh"
        "scripts/health-monitor.sh"
        "scripts/status.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ ! -f "$SCRIPT_DIR/$script" ]; then
            print_fail "Missing script: $script"
            missing_files=$((missing_files + 1))
        fi
    done
    
    if [ $missing_files -eq 0 ]; then
        print_pass "All required scripts present"
    fi
}

# Test 3: Check script executability
test_script_permissions() {
    print_test "Checking script permissions"
    
    local non_executable=0
    local scripts=(
        "midnight-burner.sh"
        "install.sh"
        "start-midnight-burner.sh"
        "stop-midnight-burner.sh"
        "restart-midnight-burner.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$SCRIPT_DIR/$script" ] && [ ! -x "$SCRIPT_DIR/$script" ]; then
            print_fail "Script not executable: $script"
            non_executable=$((non_executable + 1))
        fi
    done
    
    if [ $non_executable -eq 0 ]; then
        print_pass "All scripts are executable"
    fi
}

# Test 4: Test configuration files
test_config_files() {
    print_test "Checking configuration files"
    
    if [ -d "$SCRIPT_DIR/config" ]; then
        print_pass "Config directory exists"
    else
        print_fail "Config directory missing"
    fi
    
    if [ -d "$SCRIPT_DIR/prompts" ]; then
        if [ -f "$SCRIPT_DIR/prompts/primary-swarm.txt" ] && [ -f "$SCRIPT_DIR/prompts/secondary-swarm.txt" ]; then
            print_pass "Prompt files exist"
        else
            print_fail "Prompt files missing"
        fi
    else
        print_fail "Prompts directory missing"
    fi
}

# Test 5: Test starting and stopping
test_start_stop() {
    print_test "Testing start/stop functionality"
    
    # Try to start with test project
    export SESSION_NAME="$TEST_SESSION"
    
    if "$SCRIPT_DIR/midnight-burner.sh" "$TEST_PROJECT" >/dev/null 2>&1; then
        print_info "Started successfully"
        
        # Wait for initialization
        sleep 5
        
        # Check if session exists
        if tmux has-session -t "$TEST_SESSION" 2>/dev/null; then
            print_pass "Tmux session created"
            
            # Check process count
            local process_count=$(pgrep -f "claude-flow" | wc -l)
            if [ "$process_count" -gt 0 ]; then
                print_pass "Claude Flow processes running: $process_count"
            else
                print_fail "No Claude Flow processes found"
            fi
            
            # Test stop functionality
            if "$SCRIPT_DIR/stop-midnight-burner.sh" "$TEST_SESSION" >/dev/null 2>&1; then
                sleep 2
                if ! tmux has-session -t "$TEST_SESSION" 2>/dev/null; then
                    print_pass "Stop functionality works"
                else
                    print_fail "Session still exists after stop"
                fi
            else
                print_fail "Stop command failed"
            fi
        else
            print_fail "Tmux session not created"
        fi
    else
        print_fail "Failed to start Midnight Burner"
    fi
}

# Test 6: Test status command
test_status_command() {
    print_test "Testing status command"
    
    if [ -x "$SCRIPT_DIR/scripts/status.sh" ]; then
        if "$SCRIPT_DIR/scripts/status.sh" >/dev/null 2>&1; then
            print_pass "Status command works"
        else
            print_fail "Status command failed"
        fi
    else
        print_fail "Status script not found or not executable"
    fi
}

# Test 7: Test error handling
test_error_handling() {
    print_test "Testing error handling"
    
    # Test with non-existent directory
    if "$SCRIPT_DIR/midnight-burner.sh" "/non/existent/path" 2>&1 | grep -q "Directory not found"; then
        print_pass "Handles non-existent directory correctly"
    else
        print_fail "Does not handle non-existent directory"
    fi
    
    # Test with no arguments
    if "$SCRIPT_DIR/midnight-burner.sh" 2>&1 | grep -q "Usage:"; then
        print_pass "Shows usage when no arguments provided"
    else
        print_fail "Does not show usage message"
    fi
}

# Test 8: Test project configuration
test_project_config() {
    print_test "Testing project configuration"
    
    # Start and immediately stop to create config
    export SESSION_NAME="$TEST_SESSION"
    "$SCRIPT_DIR/midnight-burner.sh" "$TEST_PROJECT" >/dev/null 2>&1 || true
    sleep 2
    "$SCRIPT_DIR/stop-midnight-burner.sh" "$TEST_SESSION" >/dev/null 2>&1 || true
    
    # Check if project config was created
    if [ -f "$TEST_PROJECT/.midnight-burner/config.yml" ]; then
        print_pass "Project configuration created"
    else
        print_fail "Project configuration not created"
    fi
}

# Test 9: Test CLI tool functionality
test_cli_tool() {
    print_test "Testing CLI tool functionality"
    
    # Test CLI exists and is executable
    if [ -x "$SCRIPT_DIR/midnight-burner-cli.sh" ]; then
        print_pass "CLI tool is executable"
    else
        print_fail "CLI tool not found or not executable"
        return
    fi
    
    # Test help command
    if "$SCRIPT_DIR/midnight-burner-cli.sh" help 2>&1 | grep -q "Usage:"; then
        print_pass "CLI help command works"
    else
        print_fail "CLI help command failed"
    fi
    
    # Test init command with test project
    if "$SCRIPT_DIR/midnight-burner-cli.sh" init "$TEST_PROJECT" 2>&1 | grep -q "initialized\|configured"; then
        print_pass "CLI init command works"
    else
        print_fail "CLI init command failed"
    fi
    
    # Test list command
    if "$SCRIPT_DIR/midnight-burner-cli.sh" list >/dev/null 2>&1; then
        print_pass "CLI list command works"
    else
        print_fail "CLI list command failed"
    fi
}

# Test 10: Test safety mechanisms
test_safety_mechanisms() {
    print_test "Testing safety mechanisms"
    
    # Test working directory isolation
    local current_dir="$(pwd)"
    "$SCRIPT_DIR/midnight-burner.sh" "$TEST_PROJECT" >/dev/null 2>&1 || true
    sleep 2
    
    # Verify we're still in the same directory
    if [ "$(pwd)" = "$current_dir" ]; then
        print_pass "Working directory preserved"
    else
        print_fail "Working directory changed unexpectedly"
    fi
    
    # Stop test session
    "$SCRIPT_DIR/stop-midnight-burner.sh" "$TEST_SESSION" >/dev/null 2>&1 || true
    
    # Test protection against modifying midnight-burner files
    local protected_files=("$SCRIPT_DIR/README.md" "$SCRIPT_DIR/install.sh")
    local files_protected=true
    
    for file in "${protected_files[@]}"; do
        if [ -f "$file" ]; then
            local original_hash=$(sha256sum "$file" | cut -d' ' -f1)
            # Attempt to modify (this should be prevented by safety mechanisms)
            echo "test" >> "$file" 2>/dev/null || true
            local new_hash=$(sha256sum "$file" | cut -d' ' -f1)
            if [ "$original_hash" != "$new_hash" ]; then
                files_protected=false
                # Restore original file
                git checkout -- "$file" 2>/dev/null || true
            fi
        fi
    done
    
    if $files_protected; then
        print_pass "File protection mechanisms work"
    else
        print_fail "File protection mechanisms failed"
    fi
}

# Test 11: Test health monitoring
test_health_monitoring() {
    print_test "Testing health monitoring"
    
    # Check if health monitor script exists
    if [ -x "$SCRIPT_DIR/scripts/health-monitor.sh" ]; then
        print_pass "Health monitor script exists"
    else
        print_fail "Health monitor script not found"
        return
    fi
    
    # Test health check functionality
    if "$SCRIPT_DIR/scripts/health-monitor.sh" --check >/dev/null 2>&1; then
        print_pass "Health check runs successfully"
    else
        print_fail "Health check failed"
    fi
}

# Test 12: Test logging functionality
test_logging() {
    print_test "Testing logging functionality"
    
    # Start system briefly to generate logs
    export SESSION_NAME="$TEST_SESSION"
    "$SCRIPT_DIR/midnight-burner.sh" "$TEST_PROJECT" >/dev/null 2>&1 || true
    sleep 3
    "$SCRIPT_DIR/stop-midnight-burner.sh" "$TEST_SESSION" >/dev/null 2>&1 || true
    
    # Check if logs were created
    local log_files_found=0
    local expected_logs=("orchestrator.log" "artificial-user.log" "health-monitor.log")
    
    for log in "${expected_logs[@]}"; do
        if [ -f "$SCRIPT_DIR/logs/$log" ] || [ -f "$HOME/.midnight-burner/logs/$log" ]; then
            log_files_found=$((log_files_found + 1))
        fi
    done
    
    if [ $log_files_found -gt 0 ]; then
        print_pass "Log files created ($log_files_found/${#expected_logs[@]} found)"
    else
        print_fail "No log files created"
    fi
}

# Test 13: Test tmux integration
test_tmux_integration() {
    print_test "Testing tmux integration"
    
    # Test tmux availability
    if ! command -v tmux >/dev/null 2>&1; then
        print_fail "tmux not installed"
        return
    fi
    
    # Create test session
    tmux new-session -d -s "test-tmux-$$" 2>/dev/null
    
    if tmux has-session -t "test-tmux-$$" 2>/dev/null; then
        print_pass "Can create tmux sessions"
        tmux kill-session -t "test-tmux-$$" 2>/dev/null
    else
        print_fail "Cannot create tmux sessions"
    fi
    
    # Test pane splitting
    tmux new-session -d -s "test-panes-$$" 2>/dev/null
    tmux split-window -h -t "test-panes-$$" 2>/dev/null
    tmux split-window -v -t "test-panes-$$:0.0" 2>/dev/null
    
    local pane_count=$(tmux list-panes -t "test-panes-$$" 2>/dev/null | wc -l)
    if [ "$pane_count" -eq 3 ]; then
        print_pass "Pane splitting works"
    else
        print_fail "Pane splitting failed (expected 3, got $pane_count)"
    fi
    
    tmux kill-session -t "test-panes-$$" 2>/dev/null
}

# Test 14: Test cronjob setup
test_cronjob_setup() {
    print_test "Testing cronjob setup"
    
    # Check if install script has cron setup functionality
    if grep -q "crontab" "$SCRIPT_DIR/install.sh" 2>/dev/null; then
        print_pass "Cronjob setup code exists"
    else
        print_info "Cronjob setup not implemented (optional feature)"
    fi
}

# Main test execution
main() {
    print_test_header
    
    # Set up test environment
    setup_test_env
    
    # Run tests
    test_prerequisites
    test_script_files
    test_script_permissions
    test_config_files
    test_start_stop
    test_status_command
    test_error_handling
    test_project_config
    test_cli_tool
    test_safety_mechanisms
    test_health_monitoring
    test_logging
    test_tmux_integration
    test_cronjob_setup
    
    # Print summary
    print_summary
    local exit_code=$?
    
    # Cleanup
    cleanup
    
    exit $exit_code
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Run tests
main "$@"