#!/usr/bin/env bats

# Unit tests for artificial-user.sh

setup() {
  export MIDNIGHT_BURNER_ROOT="/home/<USER>/github/midnight-burner"
  export TEST_MODE=1
  export RESPONSE_INTERVAL=1  # 1 second for testing
  export USER_LOG="$MIDNIGHT_BURNER_ROOT/logs/artificial-user.log"
}

teardown() {
  unset TEST_MODE
  unset RESPONSE_INTERVAL
}

@test "artificial user script exists and is executable" {
  [ -f "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" ]
  [ -x "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" ]
}

@test "artificial user creates log file" {
  # Remove existing log
  rm -f "$USER_LOG"
  
  # Run artificial user for one response
  timeout 2s bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --single-response || true
  
  # Verify log was created
  [ -f "$USER_LOG" ]
}

@test "artificial user generates valid responses" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --generate-response
  
  # Should generate a response
  [ "$status" -eq 0 ]
  [ -n "$output" ]
  
  # Response should be meaningful
  [[ "$output" =~ "Continue" ]] || [[ "$output" =~ "next" ]] || [[ "$output" =~ "task" ]]
}

@test "artificial user respects response interval" {
  # Set short interval
  export RESPONSE_INTERVAL=2
  
  # Time the execution
  start_time=$(date +%s)
  timeout 3s bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --timed-test || true
  end_time=$(date +%s)
  
  # Should have waited at least 2 seconds
  duration=$((end_time - start_time))
  [ "$duration" -ge 2 ]
}

@test "artificial user handles tmux target correctly" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --check-target
  
  # Should identify target panes
  [[ "$output" =~ "perpetual-worker" ]] || [[ "$output" =~ "pane" ]]
}

@test "artificial user validates configuration" {
  # Test with invalid interval
  export RESPONSE_INTERVAL="invalid"
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --validate
  [ "$status" -ne 0 ]
  
  # Test with valid interval
  export RESPONSE_INTERVAL=30
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --validate
  [ "$status" -eq 0 ]
}

@test "artificial user provides contextual responses" {
  # Test different contexts
  export CONTEXT="testing"
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --generate-response
  [[ "$output" =~ "test" ]] || [[ "$output" =~ "quality" ]]
  
  export CONTEXT="development"
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --generate-response
  [[ "$output" =~ "develop" ]] || [[ "$output" =~ "implement" ]]
  
  unset CONTEXT
}

@test "artificial user handles log rotation" {
  # Create large log file
  mkdir -p "$(dirname "$USER_LOG")"
  dd if=/dev/zero of="$USER_LOG" bs=1M count=6 2>/dev/null || true
  
  # Run artificial user
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --rotate-logs
  
  # Should have rotated the log
  [ -f "$USER_LOG.1" ] || [[ "$output" =~ "rotat" ]]
  
  # Cleanup
  rm -f "$USER_LOG" "$USER_LOG.1"
}

@test "artificial user detects swarm state" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --check-swarm-state
  
  # Should detect swarm activity
  [[ "$output" =~ "swarm" ]] || [[ "$output" =~ "active" ]] || [ "$status" -eq 0 ]
}

@test "artificial user provides varied responses" {
  # Generate multiple responses
  declare -a responses
  for i in {1..5}; do
    response=$(bash "$MIDNIGHT_BURNER_ROOT/scripts/artificial-user.sh" --generate-response)
    responses+=("$response")
  done
  
  # Should have some variety (not all identical)
  unique_count=$(printf '%s\n' "${responses[@]}" | sort -u | wc -l)
  [ "$unique_count" -gt 1 ]
}