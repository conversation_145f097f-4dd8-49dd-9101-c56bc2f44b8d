# Lessons Learned - Midnight Burner

## Critical Safety Issues Discovered

### 1. Working Directory Isolation
**Problem**: Midnight Burner was running in its own directory instead of the target project directory, causing:
- Unwanted file creation in midnight-burner directory
- Inability to access target project files
- Risk of modifying midnight-burner's own files

**Solution**: Always ensure swarms run in the TARGET project directory, not midnight-burner directory.

### 2. Directory Access Restrictions
**Problem**: Claude Code sessions have directory restrictions that prevent `cd` commands outside the initial working directory.

**Solution**: Start tmux sessions with explicit working directory using `-c` flag:
```bash
tmux new-session -d -s "$SESSION_NAME" -c "$TARGET_DIR"
```

### 3. Clear Separation of Concerns
**Problem**: Confusion about where files should be created:
- Instructions (AGENTS.md, TODO.md) belong in TARGET project
- Prompts and scripts belong in midnight-burner
- Logs and state can be in midnight-burner

**Solution**: Document clear file placement rules and enforce them in scripts.

## Best Practices Established

### 1. Explicit Working Directory Verification
Always verify and display the working directory:
```bash
echo "🔒 Working in: $(pwd)"
```

### 2. Lock Swarms to Target Directory
Use loops that explicitly set directory:
```bash
cd $TARGET_DIR && while true; do
    echo "Working in: $(pwd)"
    # ... swarm command ...
done
```

### 3. Separate Control Scripts
- Control scripts (start/stop) can run from midnight-burner
- Development swarms MUST run in target project

### 4. User Confirmation
Always show the user:
- Where swarms will work
- What they can modify
- How to monitor progress

## Recommended Implementation

### CLI Tool Benefits
1. **Safety**: Validate target directory exists
2. **Clarity**: Show exactly what will happen
3. **Flexibility**: Support multiple projects
4. **Persistence**: Remember project configurations
5. **Protection**: Prevent accidental modifications

### CLI Commands Structure
```bash
midnight-burner init <project-path>    # Initialize project
midnight-burner start <project-name>   # Start development
midnight-burner status                 # Show all running projects
midnight-burner stop <project-name>    # Stop specific project
midnight-burner list                   # List configured projects
```

## Security Rules

1. **Never** allow swarms to modify midnight-burner directory
2. **Always** confirm target directory with user
3. **Validate** directory exists and is accessible
4. **Isolate** each project in its own tmux session
5. **Log** all operations for debugging

## Configuration Management

Store project configurations in:
```
~/.midnight-burner/
├── projects.json       # Project registry
├── logs/              # Centralized logs
│   └── <project>/     # Per-project logs
└── state/             # Runtime state
```

This keeps midnight-burner directory clean and projects isolated.

---
*Last Updated: August 5, 2025*