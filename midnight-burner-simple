#!/usr/bin/env bash
# Midnight Burner Simple CLI - No dependencies required

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
MIDNIGHT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$HOME/.midnight-burner"
PROJECTS_DIR="$CONFIG_DIR/projects"
LOGS_DIR="$CONFIG_DIR/logs"

# Ensure directories exist
mkdir -p "$CONFIG_DIR" "$PROJECTS_DIR" "$LOGS_DIR"

print_help() {
    echo -e "${PURPLE}🔥 Midnight Burner - Simple CLI${NC}"
    echo ""
    echo "Usage: midnight-burner <command> [options]"
    echo ""
    echo "Commands:"
    echo "  init <path>          Initialize a project"
    echo "  start <path>         Start development on a project"
    echo "  stop <name>          Stop a project"
    echo "  list                 List all projects"
    echo "  attach <name>        Attach to running project"
    echo ""
}

# Get project name from path
get_project_name() {
    basename "$1" | tr '/' '_'
}

# Initialize project
cmd_init() {
    local project_path="$1"
    
    if [[ -z "$project_path" ]]; then
        echo -e "${RED}Error: Please provide a project path${NC}"
        exit 1
    fi
    
    # Convert to absolute path
    project_path=$(cd "$project_path" 2>/dev/null && pwd) || {
        echo -e "${RED}Error: Directory '$project_path' does not exist${NC}"
        exit 1
    }
    
    local project_name=$(get_project_name "$project_path")
    
    echo -e "${BLUE}Initializing: $project_name${NC}"
    echo "Path: $project_path"
    
    # Save project info
    echo "$project_path" > "$PROJECTS_DIR/$project_name"
    
    # Create instruction files if needed
    if [[ ! -f "$project_path/AGENTS.md" ]]; then
        echo -e "${YELLOW}Creating AGENTS.md...${NC}"
        cat > "$project_path/AGENTS.md" << 'EOF'
# Midnight Burner Instructions

Work continuously on this project, creating PRs every 4 hours.

## Priorities
1. Check TODO.md for tasks
2. Fix bugs and TODO comments
3. Improve code quality
4. Add tests

Never stop working!
EOF
    fi
    
    if [[ ! -f "$project_path/TODO.md" ]]; then
        echo -e "${YELLOW}Creating TODO.md...${NC}"
        echo "# TODO" > "$project_path/TODO.md"
        echo "- [ ] Add tasks here" >> "$project_path/TODO.md"
    fi
    
    echo -e "${GREEN}✅ Initialized successfully!${NC}"
}

# Start project
cmd_start() {
    local project_path="$1"
    
    if [[ -z "$project_path" ]]; then
        echo -e "${RED}Error: Please provide a project path${NC}"
        exit 1
    fi
    
    # Handle both path and name
    if [[ -f "$PROJECTS_DIR/$(basename "$project_path")" ]]; then
        project_path=$(cat "$PROJECTS_DIR/$(basename "$project_path")")
    else
        project_path=$(cd "$project_path" 2>/dev/null && pwd) || {
            echo -e "${RED}Error: Invalid project${NC}"
            exit 1
        }
    fi
    
    local project_name=$(get_project_name "$project_path")
    local session_name="mb-$project_name"
    
    # Check if running
    if tmux has-session -t "$session_name" 2>/dev/null; then
        echo -e "${YELLOW}Already running: $project_name${NC}"
        exit 0
    fi
    
    echo -e "${BLUE}Starting: $project_name${NC}"
    echo -e "${GREEN}🔒 Working in: $project_path${NC}"
    
    # Create tmux session IN PROJECT DIRECTORY
    tmux new-session -d -s "$session_name" -c "$project_path"
    
    # Split panes
    tmux split-window -h -t "$session_name" -c "$project_path"
    tmux split-window -v -t "$session_name:0.0" -c "$project_path"
    tmux split-window -v -t "$session_name:0.1" -c "$project_path"
    
    # Start swarms LOCKED to project
    tmux send-keys -t "$session_name:0.0" "echo '🔥 Primary swarm in: $project_path' && cd '$project_path' && while true; do npx claude-flow@alpha swarm \"\$(cat '$MIDNIGHT_DIR/prompts/primary-swarm.txt')\" --claude || echo 'Restarting...'; sleep 5; done" C-m
    
    tmux send-keys -t "$session_name:0.1" "echo '🛡️ Secondary swarm in: $project_path' && cd '$project_path' && while true; do npx claude-flow@alpha swarm \"\$(cat '$MIDNIGHT_DIR/prompts/secondary-swarm.txt')\" --claude || echo 'Restarting...'; sleep 5; done" C-m
    
    tmux send-keys -t "$session_name:0.2" "cd '$MIDNIGHT_DIR' && ./scripts/artificial-user.sh '$LOGS_DIR/$project_name'" C-m
    
    tmux send-keys -t "$session_name:0.3" "cd '$MIDNIGHT_DIR' && ./scripts/health-monitor.sh '$LOGS_DIR/$project_name'" C-m
    
    echo -e "${GREEN}✅ Started!${NC}"
    echo ""
    echo "View:  midnight-burner attach $project_name"
    echo "Stop:  midnight-burner stop $project_name"
}

# Stop project
cmd_stop() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide project name${NC}"
        exit 1
    fi
    
    local session_name="mb-$project_name"
    
    if tmux has-session -t "$session_name" 2>/dev/null; then
        tmux kill-session -t "$session_name"
        echo -e "${GREEN}✅ Stopped: $project_name${NC}"
    else
        echo -e "${YELLOW}Not running: $project_name${NC}"
    fi
}

# List projects
cmd_list() {
    echo -e "${BLUE}Projects:${NC}"
    
    if [[ -z "$(ls -A "$PROJECTS_DIR" 2>/dev/null)" ]]; then
        echo "  No projects configured"
        return
    fi
    
    for proj in "$PROJECTS_DIR"/*; do
        if [[ -f "$proj" ]]; then
            local name=$(basename "$proj")
            local path=$(cat "$proj")
            local session="mb-$name"
            
            if tmux has-session -t "$session" 2>/dev/null; then
                echo -e "  ${GREEN}●${NC} $name - $path"
            else
                echo -e "  ${YELLOW}○${NC} $name - $path"
            fi
        fi
    done
}

# Attach to project
cmd_attach() {
    local project_name="$1"
    
    if [[ -z "$project_name" ]]; then
        echo -e "${RED}Error: Please provide project name${NC}"
        exit 1
    fi
    
    local session_name="mb-$project_name"
    
    if tmux has-session -t "$session_name" 2>/dev/null; then
        echo -e "${BLUE}Attaching...${NC}"
        echo "Detach: Ctrl+B, D"
        sleep 1
        tmux attach -t "$session_name"
    else
        echo -e "${RED}Not running: $project_name${NC}"
    fi
}

# Main
case "${1:-help}" in
    init)
        cmd_init "$2"
        ;;
    start)
        cmd_start "$2"
        ;;
    stop)
        cmd_stop "$2"
        ;;
    list)
        cmd_list
        ;;
    attach)
        cmd_attach "$2"
        ;;
    *)
        print_help
        ;;
esac