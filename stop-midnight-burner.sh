#!/usr/bin/env bash
# Stop Midnight Burner System
# Gracefully stops the perpetual development system

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}🛑 Midnight Burner - Stopping System${NC}"
    echo ""
}

print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Function to stop a session
stop_session() {
    local session_name="$1"
    
    if tmux has-session -t "$session_name" 2>/dev/null; then
        print_info "Stopping session: $session_name"
        tmux kill-session -t "$session_name"
        print_status "Session stopped: $session_name"
        return 0
    else
        return 1
    fi
}

# Main execution
main() {
    print_header
    
    local session_name="${1:-midnight-burner}"
    local stopped_count=0
    
    # If specific session name provided
    if [ -n "$1" ]; then
        if stop_session "$session_name"; then
            stopped_count=$((stopped_count + 1))
        else
            print_warning "Session not found: $session_name"
        fi
    else
        # Stop all midnight-burner sessions
        print_info "Looking for all Midnight Burner sessions..."
        
        # Find all sessions with 'midnight' in the name
        while IFS= read -r session; do
            if [ -n "$session" ]; then
                if stop_session "$session"; then
                    stopped_count=$((stopped_count + 1))
                fi
            fi
        done < <(tmux list-sessions 2>/dev/null | grep -i midnight | cut -d: -f1)
        
        # Also check for the default session names
        for default_session in "midnight-burner" "perpetual-worker"; do
            if stop_session "$default_session" 2>/dev/null; then
                stopped_count=$((stopped_count + 1))
            fi
        done
    fi
    
    # Clean up any remaining processes
    print_info "Cleaning up processes..."
    
    # Kill any remaining claude-flow processes related to midnight-burner
    if pgrep -f "claude-flow.*midnight" >/dev/null 2>&1; then
        pkill -f "claude-flow.*midnight" 2>/dev/null || true
        print_status "Cleaned up claude-flow processes"
    fi
    
    if pgrep -f "claude-flow.*perpetual" >/dev/null 2>&1; then
        pkill -f "claude-flow.*perpetual" 2>/dev/null || true
        print_status "Cleaned up perpetual worker processes"
    fi
    
    # Summary
    echo ""
    if [ $stopped_count -gt 0 ]; then
        print_status "Successfully stopped $stopped_count Midnight Burner session(s)"
        echo ""
        echo "🌙 The AI is taking a rest. Sweet dreams!"
    else
        print_info "No active Midnight Burner sessions found"
    fi
    
    echo ""
    print_info "To start again: ./start-midnight-burner.sh <project-path>"
}

# Run main function
main "$@"