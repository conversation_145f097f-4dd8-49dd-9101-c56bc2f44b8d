#!/usr/bin/env bash
# Start Midnight Burner System
# Quick start script for launching the perpetual development system

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}🔥 Midnight Burner - Starting System${NC}"
    echo -e "${PURPLE}The AI that never sleeps${NC}"
    echo ""
}

# Check if project path is provided
if [ -z "$1" ]; then
    print_header
    echo -e "${YELLOW}Usage: $0 <project-path|git-url>${NC}"
    echo ""
    echo "Examples:"
    echo "  $0 /path/to/your/project"
    echo "  $0 https://github.com/username/repo.git"
    echo "  $0 ."  # Current directory
    echo ""
    echo "Or use the main script directly:"
    echo "  ./midnight-burner.sh <project-path>"
    exit 1
fi

# Forward to main midnight-burner script
exec "$SCRIPT_DIR/midnight-burner.sh" "$@"