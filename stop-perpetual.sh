#!/usr/bin/env bash
# Stop Perpetual Worker System

SESSION_NAME="${SESSION_NAME:-perpetual-worker}"

echo "🛑 Stopping Perpetual Worker system..."

if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    tmux kill-session -t "$SESSION_NAME"
    echo "✅ Perpetual Worker stopped"
else
    echo "ℹ️ Perpetual Worker is not running"
fi

# Clean up any remaining processes
pkill -f "claude-flow.*perpetual" 2>/dev/null || true
echo "✅ Cleanup completed"
