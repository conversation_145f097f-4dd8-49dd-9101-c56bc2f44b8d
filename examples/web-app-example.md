# Perpetual Worker - Web App Example

This example shows how to set up Perpetual Worker for a typical web application project.

## Project Type: React/Next.js Web Application

### 1. Installation
```bash
git clone https://github.com/m-check1B/perpetual-worker.git
cd perpetual-worker
./install.sh
```

### 2. Configure for Web App
Edit `config/perpetual-config.yml`:
```yaml
project:
  name: "My Web App"
  type: "web-app"
  priority_file: "README.md"

swarms:
  primary:
    max_agents: 6
    focus: "features"  # Focus on feature development
    
  secondary:
    max_agents: 4
    focus: "testing"   # Focus on testing and QA
```

### 3. Customize Prompts
Edit `prompts/primary-swarm.txt` to include web-specific priorities:
```
🎯 WEB APP DEVELOPMENT PRIORITIES:
1. **Frontend Components**: Build reusable React components
2. **API Integration**: Connect frontend to backend APIs  
3. **User Experience**: Implement responsive design and accessibility
4. **Performance**: Optimize bundle size and loading times
5. **Testing**: Comprehensive component and integration tests
```

### 4. Project Structure
Ensure your web app has:
```
my-web-app/
├── README.md              # Project overview and priorities
├── package.json           # Dependencies and scripts
├── src/
│   ├── components/        # React components
│   ├── pages/            # Application pages
│   ├── hooks/            # Custom React hooks
│   └── utils/            # Utility functions
├── tests/                # Test files
└── docs/                 # Documentation
```

### 5. Start Perpetual Development
```bash
./start-perpetual.sh
```

### 6. Monitor Progress
```bash
# View the 4-pane development environment
tmux attach -t perpetual-worker

# Check system status
./scripts/status.sh

# View logs
tail -f logs/perpetual-worker.log
```

## Expected Behavior

The system will:
1. **Analyze** your React app structure
2. **Identify** missing components or features
3. **Implement** new features with proper testing
4. **Create** PRs with 3-8 related files
5. **Document** changes in PR_NOTES.md files
6. **Continue** 24/7 without stopping

## Common Web App Tasks

The perpetual system will typically work on:
- Building new React components
- Adding API integration
- Writing component tests
- Improving accessibility
- Optimizing performance
- Updating documentation
- Creating example usage
- Fixing bugs and issues

## Monitoring Web-Specific Metrics

The health monitor will track:
- Bundle size changes
- Test coverage percentage
- Component count and complexity
- API response times
- Build performance

## Tips for Web Apps

1. **Keep README.md updated** with current priorities and todo items
2. **Use conventional commits** for better change tracking
3. **Maintain component documentation** with examples
4. **Set up proper linting** and formatting rules
5. **Include performance budgets** in your build process