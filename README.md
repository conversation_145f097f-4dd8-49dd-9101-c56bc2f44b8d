# 🔥 Midnight Burner - Autonomous AI Development System

**The AI that never sleeps - Turn any repository into a self-sustaining, 24/7 development machine**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Claude Flow](https://img.shields.io/badge/Claude%20Flow-v2.0.0--alpha.84-blue.svg)](https://github.com/ruvnet/claude-flow)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-green.svg)]()
[![Linear](https://img.shields.io/badge/Linear-MCP%20Integrated-purple.svg)]()

## 🌟 What is Midnight Burner?

Midnight Burner creates a **fully autonomous AI development system** that burns the midnight oil so you don't have to:
- **🔄 Never stops working** - Continuous development cycles without human intervention
- **🧠 Makes smart decisions** - Context-aware development based on project priorities
- **🛡️ Self-heals automatically** - Detects and recovers from failures
- **⚡ Dual swarm power** - Primary development + secondary support swarms
- **🤖 Artificial user simulation** - Responds intelligently to keep swarms active
- **📊 Complete monitoring** - Real-time health tracking and performance metrics
- **🔗 Linear integration** - Native MCP server integration for seamless project management

## 🚀 Quick Start

### How Installation Works

**Two simple steps:**

1. **Install midnight-burner once on your server** (like installing any CLI tool)
2. **Point it at any repository** and it starts working

### Prerequisites
- Node.js 18+ 
- Git
- Unix-like environment (Linux, macOS, WSL)

### Installation (Do Once Per Server)
```bash
git clone https://github.com/m-check1B/midnight-burner.git
cd midnight-burner
./install.sh
```

### Usage (For Any Repository)

**Method 1: CLI Tool (Recommended - NEW!)**
```bash
# Initialize a project
./midnight-burner init /path/to/your/project

# Start autonomous development
./midnight-burner start project-name

# View status
./midnight-burner status

# Attach to view progress
./midnight-burner attach project-name

# Stop when needed
./midnight-burner stop project-name
```

**Method 2: Direct Command**
```bash
# Start midnight-burner on any repository
./midnight-burner.sh /path/to/your/project

# Or for remote repos
./midnight-burner.sh https://github.com/username/your-repo.git
```

**Method 3: Claude Flow Integration**
```bash
# Get the starter prompt
cat prompts/claude-flow-starter.txt

# Then in Claude Flow, paste the prompt and add:
# "Start midnight-burner on https://github.com/username/your-repo.git"
```

**Method 4: Claude Code GitHub Actions**
```bash
# One-time setup
claude /install-github-app

# Then use Claude Code directly with midnight-burner prompts
claude "Start midnight-burner autonomous development with 4-hour PR cycles"
```

## 🛡️ Safety Features (NEW!)

The CLI tool ensures:
- **Working Directory Isolation**: Swarms only work in target project directory
- **File Protection**: Cannot modify midnight-burner's own files
- **Clear Boundaries**: Each project runs in isolated tmux session
- **Project Management**: Track multiple projects safely

**That's it!** The CLI handles all safety measures automatically.

## 🎯 How It Works

### Architecture Overview
```
┌─────────────────────┬─────────────────────┐
│   Primary Swarm     │   Secondary Swarm   │
│ (Main Development)  │  (Support & QA)     │
│ • Feature dev       │ • Testing           │
│ • Bug fixes         │ • Documentation     │
│ • Implementation    │ • Code review       │
├─────────────────────┼─────────────────────┤
│  Artificial User    │  Health Monitor     │
│ • Smart responses   │ • Process tracking  │
│ • Context awareness │ • Auto-recovery     │
│ • Activity maintenance│ • Performance logs │
└─────────────────────┴─────────────────────┘
```

### The Perpetual Cycle
1. **Assessment** - Analyze project state and priorities
2. **Execution** - Work on highest priority tasks
3. **Transition** - Save progress and start next cycle
4. **Never Exit** - Continuous loop, no completion state

## 📋 Features

### ✅ Core Features
- **Dual Swarm System** - Primary development + secondary support
- **Artificial User Agent** - 20+ intelligent response patterns  
- **Health Monitoring** - Real-time process tracking and recovery
- **Tmux Integration** - Organized 4-pane development interface
- **Cronjob Automation** - Automatic startup and maintenance
- **State Persistence** - Survives reboots and interruptions

### ✅ Advanced Features  
- **Claude Flow Integration** - Enterprise-grade AI orchestration
- **Custom Prompts** - Configurable behavior for any project type
- **Multi-Project Support** - Deploy across different repositories
- **Performance Analytics** - Detailed metrics and reporting
- **PR Generation** - Automatic pull request creation with documentation
- **Error Recovery** - Multiple levels of failure handling

### 🔗 Linear Integration Features
- **Native MCP Server** - Uses Linear's official MCP server for seamless integration
- **Automatic Issue Assignment** - AI automatically finds and claims issues to work on
- **Real-time Status Updates** - Issues are updated as work progresses
- **Intelligent Prioritization** - Works on urgent/high priority issues first
- **Progress Comments** - Detailed updates added to Linear issues automatically
- **PR Linking** - Pull requests automatically linked to Linear issues
- **Smart Issue Creation** - Creates new issues for bugs/improvements found during development

## 🛠️ Configuration

### Basic Configuration
Edit `config/perpetual-config.yml`:
```yaml
project:
  name: "Your Project Name"
  type: "web-app"  # web-app, api, library, etc.
  priority_file: "PRIORITIES.md"  # Your project priorities

swarms:
  primary:
    max_agents: 5
    strategy: "auto"
    mode: "centralized"
  secondary:
    max_agents: 3
    strategy: "support"
    mode: "distributed"

monitoring:
  health_check_interval: 60  # seconds
  max_restart_attempts: 3
  log_retention_days: 7
```

### Custom Prompts
Customize behavior by editing files in `prompts/`:
- `primary-swarm.txt` - Main development instructions
- `secondary-swarm.txt` - Support swarm behavior
- `artificial-user.txt` - User response patterns

## 📊 Monitoring & Control

### View the System
```bash
# Attach to running system (4-pane view)
tmux attach -t perpetual-worker

# Check system status
./scripts/status.sh

# View logs
tail -f logs/perpetual-worker.log
```

### Control Commands
```bash
# Start system
./start-perpetual.sh

# Stop system
./stop-perpetual.sh

# Restart system
./restart-perpetual.sh

# Health check
./scripts/health-check.sh
```

## 🎮 Usage Examples

### For Web Applications
```bash
# Setup for React/Next.js project
./scripts/setup-perpetual.sh ~/my-react-app --type web-app
```

### For API Development
```bash  
# Setup for API project
./scripts/setup-perpetual.sh ~/my-api --type api --focus backend
```

### For Libraries/Packages
```bash
# Setup for package development
./scripts/setup-perpetual.sh ~/my-package --type library --focus testing
```

## 📈 Performance

### Verified Results
- **24/7 Operation** - Continuous development without breaks
- **Auto-Recovery** - <2 minute recovery time from failures
- **Smart Decision Making** - Context-aware task prioritization
- **Process Stability** - Maintains 8-13 active AI processes
- **Memory Efficiency** - Built-in cleanup and optimization

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: 2+ cores recommended
- **Disk**: 1GB for logs and state files
- **Network** Stable internet for Claude API calls

## 🔧 Troubleshooting

### Common Issues

**Q: System stops working after a few hours**
```bash
# Check process count
pgrep -f "claude-flow" | wc -l

# If count is low, restart
./restart-perpetual.sh
```

**Q: Swarms are not responding**
```bash
# Check artificial user activity
tail -f logs/artificial-user.log

# Manually send commands
tmux send-keys -t perpetual-worker:0.0 "Continue development" C-m
```

**Q: High CPU usage**
```bash
# Check system health
./scripts/health-check.sh

# Adjust agent count in config/perpetual-config.yml
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Test your changes with the test suite: `./test-perpetual.sh`
4. Submit a pull request

## 📜 License

MIT License - feel free to use in your projects!

## 🙏 Acknowledgments

- Built on [Claude Flow](https://github.com/ruvnet/claude-flow) enterprise orchestration
- Inspired by autonomous development workflows
- Tested on real-world production projects

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/m-check1B/midnight-burner/issues)
- **Discussions**: [GitHub Discussions](https://github.com/m-check1B/midnight-burner/discussions)
- **Email**: <EMAIL>

---

**🔥 Ready to turn your repo into a 24/7 development machine?**

```bash
git clone https://github.com/m-check1B/midnight-burner.git && cd midnight-burner && ./install.sh
```