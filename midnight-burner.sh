#!/usr/bin/env bash
# Midnight Burner - Main Entry Point
# The AI that never sleeps - Turn any repository into a self-sustaining, 24/7 development machine

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$SCRIPT_DIR/config"
PROMPTS_DIR="$SCRIPT_DIR/prompts"
LOGS_DIR="$SCRIPT_DIR/logs"

# Configuration
DEFAULT_SESSION_NAME="midnight-burner"
DEFAULT_CONFIG_FILE="$CONFIG_DIR/midnight-burner-config.yml"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_header() {
    echo -e "${PURPLE}🔥 Midnight Burner${NC}"
    echo -e "${PURPLE}The AI that never sleeps${NC}"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    local missing_deps=0
    
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is required but not installed"
        missing_deps=1
    fi
    
    if ! command -v git >/dev/null 2>&1; then
        print_error "Git is required but not installed"
        missing_deps=1
    fi
    
    if ! command -v tmux >/dev/null 2>&1; then
        print_error "tmux is required but not installed"
        missing_deps=1
    fi
    
    if ! command -v claude >/dev/null 2>&1; then
        print_warning "Claude Code not found. Run ./install.sh first"
        missing_deps=1
    fi
    
    if [ $missing_deps -eq 1 ]; then
        echo ""
        print_info "Please install missing dependencies or run ./install.sh"
        exit 1
    fi
}

# Function to setup project directory
setup_project() {
    local project_path="$1"
    
    if [ -z "$project_path" ]; then
        print_error "No project path specified"
        echo ""
        echo "Usage: $0 <project-path|git-url>"
        echo ""
        echo "Examples:"
        echo "  $0 /path/to/your/project"
        echo "  $0 https://github.com/username/repo.git"
        exit 1
    fi
    
    # Check if it's a git URL
    if [[ "$project_path" =~ ^https?:// ]] || [[ "$project_path" =~ ^git@ ]]; then
        print_info "Cloning repository: $project_path"
        local repo_name=$(basename "$project_path" .git)
        local target_dir="/tmp/midnight-burner-projects/$repo_name"
        
        mkdir -p "$(dirname "$target_dir")"
        
        if [ -d "$target_dir" ]; then
            print_info "Repository already cloned, pulling latest changes..."
            cd "$target_dir"
            git pull
        else
            git clone "$project_path" "$target_dir"
            cd "$target_dir"
        fi
        
        PROJECT_PATH="$target_dir"
    else
        # Local directory
        if [ ! -d "$project_path" ]; then
            print_error "Directory not found: $project_path"
            exit 1
        fi
        
        PROJECT_PATH="$(cd "$project_path" && pwd)"
        cd "$PROJECT_PATH"
    fi
    
    print_status "Working on project: $PROJECT_PATH"
}

# Function to configure project-specific settings
configure_project() {
    local project_name=$(basename "$PROJECT_PATH")
    local project_config="$PROJECT_PATH/.midnight-burner/config.yml"
    
    # Create project-specific config directory
    mkdir -p "$PROJECT_PATH/.midnight-burner"
    
    # Copy or create project configuration
    if [ ! -f "$project_config" ]; then
        print_info "Creating project configuration..."
        cat > "$project_config" << EOF
# Midnight Burner Configuration for $project_name

project:
  name: "$project_name"
  type: "auto-detect"  # web-app, api, library, general
  priority_file: "README.md"  # File containing project priorities
  
swarms:
  primary:
    max_agents: 5
    strategy: "auto"
    mode: "centralized"
    focus: "development"
    
  secondary:
    max_agents: 3
    strategy: "support"
    mode: "distributed"
    focus: "testing,documentation"

development:
  pr_cycle_hours: 4  # Create PRs every 4 hours
  pr_size_limit: 8   # Max files per PR
  auto_commit: true
  auto_pr: true
  
monitoring:
  health_check_interval: 60
  max_restart_attempts: 3
  log_retention_days: 7
EOF
        print_status "Project configuration created"
    fi
    
    # Export configuration
    export MIDNIGHT_BURNER_CONFIG="$project_config"
    export MIDNIGHT_BURNER_PROJECT="$PROJECT_PATH"
    export MIDNIGHT_BURNER_SESSION="${project_name}-midnight"
}

# Function to start perpetual worker
start_perpetual_worker() {
    local session_name="${MIDNIGHT_BURNER_SESSION:-$DEFAULT_SESSION_NAME}"
    
    print_header
    print_info "Starting Midnight Burner for: $(basename "$PROJECT_PATH")"
    
    # Check if session already exists
    if tmux has-session -t "$session_name" 2>/dev/null; then
        print_warning "Midnight Burner is already running for this project"
        print_info "Attach with: tmux attach -t $session_name"
        exit 0
    fi
    
    # Create logs directory
    mkdir -p "$LOGS_DIR"
    
    # Start the perpetual orchestrator
    print_info "Launching perpetual development system..."
    
    # Set environment variables
    export PROJECT_ROOT="$PROJECT_PATH"
    export SESSION_NAME="$session_name"
    export CONFIG_FILE="$MIDNIGHT_BURNER_CONFIG"
    
    # Start the orchestrator
    "$SCRIPT_DIR/scripts/perpetual-orchestrator.sh" &
    
    # Wait for system to initialize
    sleep 5
    
    # Check if started successfully
    if tmux has-session -t "$session_name" 2>/dev/null; then
        print_status "Midnight Burner started successfully!"
        echo ""
        echo "🎯 System is now running autonomously on: $PROJECT_PATH"
        echo ""
        echo "📺 View the system:"
        echo "   tmux attach -t $session_name"
        echo ""
        echo "🛑 Stop the system:"
        echo "   $SCRIPT_DIR/stop-midnight-burner.sh $session_name"
        echo ""
        echo "📊 Check status:"
        echo "   $SCRIPT_DIR/scripts/status.sh"
        echo ""
        print_info "The AI is now burning the midnight oil on your project! 🔥"
    else
        print_error "Failed to start Midnight Burner"
        exit 1
    fi
}

# Main execution
main() {
    # Check prerequisites
    check_prerequisites
    
    # Setup project
    setup_project "$1"
    
    # Configure project
    configure_project
    
    # Start perpetual worker
    start_perpetual_worker
}

# Run main function
main "$@"