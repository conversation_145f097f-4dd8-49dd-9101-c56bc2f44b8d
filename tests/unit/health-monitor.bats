#!/usr/bin/env bats

# Unit tests for health-monitor.sh

setup() {
  export MIDNIGHT_BURNER_ROOT="/home/<USER>/github/midnight-burner"
  export TEST_MODE=1
  export CHECK_INTERVAL=1  # 1 second for testing
  export HEALTH_LOG="$MIDNIGHT_BURNER_ROOT/logs/health-monitor.log"
}

teardown() {
  unset TEST_MODE
  unset CHECK_INTERVAL
}

@test "health monitor script exists and is executable" {
  [ -f "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" ]
  [ -x "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" ]
}

@test "health monitor creates log file" {
  # Remove existing log
  rm -f "$HEALTH_LOG"
  
  # Run health monitor for one check
  timeout 2s bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --single-check || true
  
  # Verify log was created
  [ -f "$HEALTH_LOG" ]
}

@test "health monitor detects Claude processes" {
  # Run check and capture output
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --check-processes
  
  # Should report process count
  [[ "$output" =~ "Processes:" ]] || [[ "$output" =~ "claude" ]]
}

@test "health monitor reports memory usage" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --check-memory
  
  # Should report memory percentage
  [[ "$output" =~ "Memory:" ]] || [[ "$output" =~ "%" ]]
}

@test "health monitor reports system load" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --check-load
  
  # Should report load average
  [[ "$output" =~ "Load:" ]] || [[ "$output" =~ "[0-9]" ]]
}

@test "health monitor handles restart threshold" {
  # Test with high memory usage simulation
  export SIMULATE_HIGH_MEMORY=95
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --check-restart
  
  # Should recommend restart
  [[ "$output" =~ "restart" ]] || [ "$status" -eq 1 ]
  
  unset SIMULATE_HIGH_MEMORY
}

@test "health monitor validates check interval" {
  # Test with invalid interval
  export CHECK_INTERVAL="invalid"
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --validate
  [ "$status" -ne 0 ]
  
  # Test with valid interval
  export CHECK_INTERVAL=60
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --validate
  [ "$status" -eq 0 ]
}

@test "health monitor handles log rotation" {
  # Create large log file (simulate)
  mkdir -p "$(dirname "$HEALTH_LOG")"
  dd if=/dev/zero of="$HEALTH_LOG" bs=1M count=11 2>/dev/null || true
  
  # Run health monitor
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --rotate-logs
  
  # Should have rotated the log
  [ -f "$HEALTH_LOG.1" ] || [[ "$output" =~ "rotat" ]]
  
  # Cleanup
  rm -f "$HEALTH_LOG" "$HEALTH_LOG.1"
}

@test "health monitor calculates process efficiency" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --efficiency
  
  # Should report efficiency metrics
  [[ "$output" =~ "efficiency" ]] || [[ "$output" =~ "CPU" ]]
}

@test "health monitor integrates with tmux session" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/health-monitor.sh" --check-tmux
  
  # Should check tmux session status
  [[ "$output" =~ "tmux" ]] || [[ "$output" =~ "perpetual-worker" ]]
}