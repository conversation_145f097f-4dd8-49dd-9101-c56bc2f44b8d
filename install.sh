#!/usr/bin/env bash
# Midnight Burner - One-Command Installation Script
# Makes any repository autonomous with 24/7 AI development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${PROJECT_ROOT:-$(pwd)}"

# Configuration
DEFAULT_SESSION_NAME="midnight-burner"
DEFAULT_CONFIG_FILE="$SCRIPT_DIR/config/midnight-burner-config.yml"

echo -e "${BLUE}🔥 Midnight Burner Installation${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "${PURPLE}The AI that never sleeps${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Function to install Claude Code
install_claude_code() {
    print_info "Checking Claude Code installation..."
    
    if command -v claude >/dev/null 2>&1; then
        local version=$(claude --version 2>/dev/null || echo "unknown")
        print_status "Claude Code already installed: $version"
        return 0
    fi
    
    print_info "Installing Claude Code..."
    
    # Try different installation methods
    if command -v npm >/dev/null 2>&1; then
        print_info "Installing Claude Code via npm..."
        npm install -g @anthropics/claude-code 2>/dev/null || {
            print_warning "npm install failed, trying alternative installation..."
            
            # Try curl installation
            if command -v curl >/dev/null 2>&1; then
                print_info "Installing Claude Code via curl..."
                curl -fsSL https://install.anthropic.com/claude-code | bash
            else
                print_error "Unable to install Claude Code. Please install manually from https://docs.anthropic.com/claude-code"
                return 1
            fi
        }
    else
        print_error "npm not found. Please install Node.js and npm first."
        return 1
    fi
    
    # Verify installation
    if command -v claude >/dev/null 2>&1; then
        local version=$(claude --version 2>/dev/null || echo "installed")
        print_status "Claude Code installed successfully: $version"
    else
        print_error "Claude Code installation failed"
        return 1
    fi
}

# Function to install/update Claude Flow
install_claude_flow() {
    print_info "Checking Claude Flow installation..."
    
    # Check if claude-flow is installed
    if command -v claude-flow >/dev/null 2>&1; then
        local current_version=$(claude-flow --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1 || echo "unknown")
        print_info "Claude Flow current version: $current_version"
        
        # Update to latest stable
        print_info "Updating Claude Flow to latest stable version..."
        npm update -g claude-flow || {
            print_warning "Update failed, reinstalling..."
            npm uninstall -g claude-flow 2>/dev/null || true
            npm install -g claude-flow@latest
        }
    else
        print_info "Installing Claude Flow..."
        npm install -g claude-flow@latest || {
            print_error "Failed to install Claude Flow via npm"
            return 1
        }
    fi
    
    # Verify installation
    if command -v claude-flow >/dev/null 2>&1; then
        local version=$(claude-flow --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1 || echo "installed")
        print_status "Claude Flow ready: $version"
    else
        print_error "Claude Flow installation failed"
        return 1
    fi
}

# Function to setup Claude Code GitHub integration
setup_claude_code_github() {
    print_info "Setting up Claude Code GitHub integration..."
    
    print_info "Please run the following command to set up GitHub integration:"
    echo -e "${YELLOW}claude /install-github-app${NC}"
    echo ""
    echo "This will:"
    echo "  • Install GitHub App for Claude Code"
    echo "  • Set up authentication and permissions"  
    echo "  • Enable automatic PR creation"
    echo ""
    
    read -p "Press Enter when you've completed GitHub setup..."
    print_status "GitHub integration setup completed"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is required but not installed."
        print_info "Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    print_status "Node.js $(node -v) found"
    
    # Check npm
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is required but not installed."
        exit 1
    fi
    print_status "npm $(npm -v) found"
    
    # Check git
    if ! command -v git >/dev/null 2>&1; then
        print_error "Git is required but not installed."
        exit 1
    fi
    print_status "Git $(git --version | cut -d' ' -f3) found"
    
    # Check tmux
    if ! command -v tmux >/dev/null 2>&1; then
        print_warning "tmux not found. Installing..."
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y tmux
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y tmux
        elif command -v brew >/dev/null 2>&1; then
            brew install tmux
        else
            print_error "Could not install tmux automatically. Please install it manually."
            exit 1
        fi
    fi
    print_status "tmux found"
}

# Install Claude Flow
install_claude_flow() {
    print_info "Installing Claude Flow..."
    
    if npm list -g claude-flow@alpha >/dev/null 2>&1; then
        print_status "Claude Flow already installed"
    else
        npm install -g claude-flow@alpha
        print_status "Claude Flow installed"
    fi
    
    # Verify installation
    if ! command -v claude-flow >/dev/null 2>&1; then
        print_error "Claude Flow installation failed"
        exit 1
    fi
    
    local cf_version=$(npx claude-flow@alpha --version 2>/dev/null | head -n1)
    print_status "Claude Flow $cf_version ready"
}

# Create directory structure
create_directories() {
    print_info "Creating directory structure..."
    
    mkdir -p "$SCRIPT_DIR"/{scripts,prompts,config,logs,state,examples}
    mkdir -p "$PROJECT_ROOT"/.perpetual-worker
    
    print_status "Directory structure created"
}

# Copy and adapt core scripts
create_core_scripts() {
    print_info "Creating core scripts..."
    
    # Main orchestrator script
    cat > "$SCRIPT_DIR/scripts/perpetual-orchestrator.sh" << 'EOF'
#!/usr/bin/env bash
# Perpetual Worker - Main Orchestrator
# Maintains continuous development flow using Claude Flow

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="${CONFIG_FILE:-$PROJECT_ROOT/config/perpetual-config.yml}"
SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
STATE_DIR="$PROJECT_ROOT/state"
LOG_DIR="$PROJECT_ROOT/logs"

# Ensure directories exist
mkdir -p "$STATE_DIR" "$LOG_DIR"

# Configuration defaults
PRIMARY_AGENTS=${PRIMARY_AGENTS:-5}
SECONDARY_AGENTS=${SECONDARY_AGENTS:-3}
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-60}

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/orchestrator.log"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOG_DIR/orchestrator.log"
}

# Check if session exists
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    log_info "Perpetual Worker session already exists. Use 'tmux attach -t $SESSION_NAME' to view."
    exit 0
fi

log_info "Starting Perpetual Worker system..."

# Create tmux session with 4 panes
tmux new-session -d -s "$SESSION_NAME" -x 120 -y 30

# Split into 4 panes
tmux split-window -h -t "$SESSION_NAME"
tmux split-window -v -t "$SESSION_NAME:0.0"
tmux split-window -v -t "$SESSION_NAME:0.1"

# Set working directory for all panes
for pane in 0 1 2 3; do
    tmux send-keys -t "$SESSION_NAME:0.$pane" "cd '$PROJECT_ROOT'" C-m
done

# Start primary swarm (pane 0)
log_info "Starting primary development swarm..."
tmux send-keys -t "$SESSION_NAME:0.0" "while true; do npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/primary-swarm.txt')\" --claude || echo 'Primary swarm restarting...'; sleep 5; done" C-m

# Start secondary swarm (pane 1) 
log_info "Starting secondary support swarm..."
tmux send-keys -t "$SESSION_NAME:0.1" "while true; do npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/secondary-swarm.txt')\" --claude || echo 'Secondary swarm restarting...'; sleep 5; done" C-m

# Start artificial user (pane 2)
log_info "Starting artificial user agent..."
tmux send-keys -t "$SESSION_NAME:0.2" "'$SCRIPT_DIR/artificial-user.sh' '$LOG_DIR'" C-m

# Start health monitor (pane 3)
log_info "Starting health monitor..."
tmux send-keys -t "$SESSION_NAME:0.3" "'$SCRIPT_DIR/health-monitor.sh' '$LOG_DIR'" C-m

log_info "Perpetual Worker system started successfully!"
log_info "View with: tmux attach -t $SESSION_NAME"
log_info "Stop with: $SCRIPT_DIR/stop-perpetual.sh"

# Give user instructions
echo ""
echo "🎉 Perpetual Worker is now running!"
echo ""
echo "📺 View the system:"
echo "   tmux attach -t $SESSION_NAME"
echo ""
echo "🛑 Stop the system:"
echo "   $PROJECT_ROOT/stop-perpetual.sh"
echo ""
echo "📊 Check status:"
echo "   $SCRIPT_DIR/status.sh"
EOF

    chmod +x "$SCRIPT_DIR/scripts/perpetual-orchestrator.sh"
    
    # Artificial user script
    cat > "$SCRIPT_DIR/scripts/artificial-user.sh" << 'EOF'
#!/usr/bin/env bash
# Artificial User Agent - Keeps swarms active with intelligent responses

LOG_DIR="${1:-logs}"
SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

mkdir -p "$LOG_DIR"

log_activity() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_DIR/artificial-user.log"
}

# Response patterns
responses=(
    "Continue with the next priority task"
    "Analyze current progress and implement next feature"
    "Work on the active development tasks systematically"
    "Check project status and proceed with development"
    "Keep building - focus on high priority items"
    "Good progress, continue with next phase"
    "Proceed with the implementation"
    "Continue development workflow"
    "Analyze and continue with next task"
    "Maintain development momentum"
)

get_random_response() {
    echo "${responses[$RANDOM % ${#responses[@]}]}"
}

log_activity "Artificial user agent started"

while true; do
    # Check if swarms need interaction
    sleep $((30 + RANDOM % 30))  # 30-60 second intervals
    
    # Check process health
    process_count=$(pgrep -f "claude-flow" | wc -l)
    
    if [ "$process_count" -lt 2 ]; then
        log_activity "Low process count ($process_count), restarting swarms..."
        tmux send-keys -t "$SESSION_NAME:0.0" C-c
        tmux send-keys -t "$SESSION_NAME:0.1" C-c
        sleep 2
        tmux send-keys -t "$SESSION_NAME:0.0" "npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/primary-swarm.txt')\" --claude" C-m
        tmux send-keys -t "$SESSION_NAME:0.1" "npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/secondary-swarm.txt')\" --claude" C-m
    else
        # Send encouraging response to primary swarm
        response=$(get_random_response)
        log_activity "Sending response: $response"
        tmux send-keys -t "$SESSION_NAME:0.0" "$response" C-m
    fi
done
EOF

    chmod +x "$SCRIPT_DIR/scripts/artificial-user.sh"
    
    # Health monitor script
    cat > "$SCRIPT_DIR/scripts/health-monitor.sh" << 'EOF'
#!/usr/bin/env bash
# Health Monitor - Tracks system performance and recovery

LOG_DIR="${1:-logs}"
SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-60}"

mkdir -p "$LOG_DIR"

log_health() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_DIR/health-monitor.log"
}

log_health "Health monitor started (check interval: ${HEALTH_CHECK_INTERVAL}s)"

while true; do
    # System health checks
    process_count=$(pgrep -f "claude-flow" | wc -l)
    memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    load_average=$(uptime | awk -F'load average:' '{print $2}' | cut -d',' -f1 | tr -d ' ')
    
    # Log health metrics
    log_health "Processes: $process_count, Memory: ${memory_usage}%, Load: $load_average"
    
    # Display in monitor pane
    clear
    echo "🔄 Perpetual Worker - Health Monitor"
    echo "=================================="
    echo ""
    echo "📊 System Status:"
    echo "   Claude Flow Processes: $process_count"
    echo "   Memory Usage: ${memory_usage}%"
    echo "   Load Average: $load_average"
    echo "   Last Check: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo "📋 Active Components:"
    if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
        echo "   ✅ Primary Swarm (Pane 0)"
        echo "   ✅ Secondary Swarm (Pane 1)"
        echo "   ✅ Artificial User (Pane 2)" 
        echo "   ✅ Health Monitor (Pane 3)"
    else
        echo "   ❌ Session not found"
    fi
    echo ""
    echo "🔄 Next check in ${HEALTH_CHECK_INTERVAL} seconds..."
    
    sleep "$HEALTH_CHECK_INTERVAL"
done
EOF

    chmod +x "$SCRIPT_DIR/scripts/health-monitor.sh"
    
    print_status "Core scripts created"
}

# Create control scripts
create_control_scripts() {
    print_info "Creating control scripts..."
    
    # Start script
    cat > "$SCRIPT_DIR/start-perpetual.sh" << 'EOF'
#!/usr/bin/env bash
# Start Perpetual Worker System

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
exec "$SCRIPT_DIR/scripts/perpetual-orchestrator.sh" "$@"
EOF

    # Stop script
    cat > "$SCRIPT_DIR/stop-perpetual.sh" << 'EOF'
#!/usr/bin/env bash
# Stop Perpetual Worker System

SESSION_NAME="${SESSION_NAME:-perpetual-worker}"

echo "🛑 Stopping Perpetual Worker system..."

if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    tmux kill-session -t "$SESSION_NAME"
    echo "✅ Perpetual Worker stopped"
else
    echo "ℹ️ Perpetual Worker is not running"
fi

# Clean up any remaining processes
pkill -f "claude-flow.*perpetual" 2>/dev/null || true
echo "✅ Cleanup completed"
EOF

    # Restart script
    cat > "$SCRIPT_DIR/restart-perpetual.sh" << 'EOF'
#!/usr/bin/env bash
# Restart Perpetual Worker System

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🔄 Restarting Perpetual Worker system..."
"$SCRIPT_DIR/stop-perpetual.sh"
sleep 3
"$SCRIPT_DIR/start-perpetual.sh"
EOF

    # Status script
    cat > "$SCRIPT_DIR/scripts/status.sh" << 'EOF'
#!/usr/bin/env bash
# Check Perpetual Worker Status

SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🔄 Perpetual Worker Status"
echo "========================="
echo ""

# Check session status
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    echo "📺 Tmux Session: ✅ Active ($SESSION_NAME)"
    echo "   Panes: $(tmux list-panes -t "$SESSION_NAME" | wc -l)"
else
    echo "📺 Tmux Session: ❌ Not running"
fi

# Check processes
process_count=$(pgrep -f "claude-flow" | wc -l)
echo "🤖 Claude Flow Processes: $process_count"

# Check logs
if [ -f "$PROJECT_ROOT/logs/orchestrator.log" ]; then
    last_activity=$(tail -n1 "$PROJECT_ROOT/logs/orchestrator.log" | cut -d']' -f1 | tr -d '[')
    echo "📝 Last Activity: $last_activity"
fi

echo ""
echo "🎮 Control Commands:"
echo "   View system: tmux attach -t $SESSION_NAME"
echo "   Stop system: $PROJECT_ROOT/stop-perpetual.sh"
echo "   Restart: $PROJECT_ROOT/restart-perpetual.sh"
EOF

    # Make all scripts executable
    chmod +x "$SCRIPT_DIR"/{start-perpetual.sh,stop-perpetual.sh,restart-perpetual.sh}
    chmod +x "$SCRIPT_DIR/scripts/status.sh"
    
    print_status "Control scripts created"
}

# Create default prompts
create_prompts() {
    print_info "Creating default prompts..."
    
    # Primary swarm prompt
    cat > "$SCRIPT_DIR/prompts/primary-swarm.txt" << 'EOF'
🎯 PERPETUAL PRIMARY DEVELOPMENT SWARM

You are the PRIMARY development swarm for continuous, autonomous software development.

🔄 PERPETUAL DEVELOPMENT CYCLE:
After completing any task, you MUST:
1. ANALYZE: Check project state and priorities (README.md, TODO.md, issues)
2. DECIDE: Select next highest priority task
3. EXECUTE: Work on the task systematically with proper testing
4. TRANSITION: Save progress and IMMEDIATELY start next cycle
5. NEVER EXIT: Always continue to next development cycle

🎯 DEVELOPMENT PRIORITIES:
1. Read project documentation to understand current state
2. Check for TODO items, issues, or incomplete features
3. Implement features with proper testing and documentation
4. Create reasonably-sized PRs (3-8 files) with PR_NOTES.md
5. Maintain code quality and follow project conventions

🚨 MANDATORY BEHAVIOR:
- NEVER say "task completed" and exit
- ALWAYS ask "What should I work on next?" and continue
- CONTINUOUSLY cycle through development phases
- Create meaningful PRs with documentation
- Keep the development momentum going 24/7

🎯 START NOW: Begin analyzing the project and start your first development cycle.
EOF

    # Secondary swarm prompt
    cat > "$SCRIPT_DIR/prompts/secondary-swarm.txt" << 'EOF'
🤖 PERPETUAL SECONDARY SUPPORT SWARM

You are the SECONDARY support swarm providing continuous assistance to the primary development swarm.

🔄 PERPETUAL SUPPORT CYCLE:
After completing any support task, you MUST:
1. MONITOR: Check primary swarm progress and needs
2. IDENTIFY: Next support task (testing, docs, optimization, review)
3. EXECUTE: Provide support while monitoring primary swarm
4. CONTINUE: Report completion and IMMEDIATELY start next support cycle
5. NEVER EXIT: Always continue supporting

🎯 SUPPORT PRIORITIES:
1. **Testing**: Write/run tests for new primary swarm code
2. **Documentation**: Update docs, README, and API documentation
3. **Code Review**: Analyze code quality and suggest improvements
4. **Performance**: Monitor and optimize system performance
5. **Quality Assurance**: Ensure best practices and standards

🚨 MANDATORY BEHAVIOR:
- NEVER say "support task completed" and exit
- ALWAYS continue monitoring and supporting
- COMPLEMENT the primary swarm's work
- Focus on quality, testing, and documentation
- Maintain continuous support 24/7

🎯 START NOW: Begin monitoring the primary swarm and provide your first support task.
EOF

    print_status "Default prompts created"
}

# Create configuration file
create_config() {
    print_info "Creating configuration file..."
    
    cat > "$SCRIPT_DIR/config/perpetual-config.yml" << 'EOF'
# Perpetual Worker Configuration

project:
  name: "Perpetual Worker Project"
  type: "general"  # web-app, api, library, general
  priority_file: "README.md"  # File containing project priorities
  
swarms:
  primary:
    max_agents: 5
    strategy: "auto"
    mode: "centralized"
    timeout_minutes: 60
    
  secondary:
    max_agents: 3
    strategy: "support"
    mode: "distributed"
    timeout_minutes: 45

monitoring:
  health_check_interval: 60  # seconds
  max_restart_attempts: 3
  log_retention_days: 7
  process_threshold: 2  # minimum processes before restart

session:
  name: "perpetual-worker"
  auto_attach: false
  
automation:
  cronjob: true
  startup_delay: 10  # seconds
  recovery_delay: 5   # seconds between restart attempts
EOF

    print_status "Configuration file created"
}

# Main installation flow
main() {
    echo -e "${PURPLE}Starting Midnight Burner installation...${NC}"
    echo ""
    
    # Core installation steps
    check_prerequisites
    install_claude_code
    install_claude_flow
    create_directories
    create_core_scripts
    create_control_scripts
    create_prompts
    create_config
    
    echo ""
    echo -e "${GREEN}🎉 Midnight Burner installation completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📚 Next Steps:${NC}"
    echo "1. Set up GitHub integration: ${YELLOW}claude /install-github-app${NC}"
    echo "2. Customize prompts in prompts/ directory for your project"
    echo "3. Edit config/midnight-burner-config.yml for your needs"
    echo "4. Start the system: ./start-midnight-burner.sh"
    echo "5. View the system: tmux attach -t midnight-burner"
    echo ""
    echo -e "${BLUE}🚀 Usage Options:${NC}"
    echo "• ${YELLOW}Method 1:${NC} ./midnight-burner.sh /path/to/your/project"
    echo "• ${YELLOW}Method 2:${NC} claude \"Start midnight-burner autonomous development\""
    echo "• ${YELLOW}Method 3:${NC} Use Claude Flow with midnight-burner prompts"
    echo ""
    echo -e "${YELLOW}🔥 Ready to make any repository autonomous!${NC}"
    echo -e "${PURPLE}The AI that never sleeps is ready to work${NC}"
}

# Run installation
main "$@"