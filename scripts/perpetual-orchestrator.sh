#!/usr/bin/env bash
# Perpetual Worker - Main Orchestrator
# Maintains continuous development flow using Claude Flow

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="${CONFIG_FILE:-$PROJECT_ROOT/config/perpetual-config.yml}"
SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
STATE_DIR="$PROJECT_ROOT/state"
LOG_DIR="$PROJECT_ROOT/logs"

# Ensure directories exist
mkdir -p "$STATE_DIR" "$LOG_DIR"

# Configuration defaults
PRIMARY_AGENTS=${PRIMARY_AGENTS:-5}
SECONDARY_AGENTS=${SECONDARY_AGENTS:-5}
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-60}

# Initialize secondary swarm with 5 specialized agents
initialize_secondary_swarm() {
    log_info "Initializing secondary swarm with 5 specialized agents..."
    "$SCRIPT_DIR/task-structure-manager.sh"
    log_info "Secondary swarm initialization complete"
}

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/orchestrator.log"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOG_DIR/orchestrator.log"
}

# Check if session exists
if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    log_info "Perpetual Worker session already exists. Use 'tmux attach -t $SESSION_NAME' to view."
    exit 0
fi

log_info "Starting Perpetual Worker system with 5-agent secondary swarm..."

# Initialize secondary swarm structure
initialize_secondary_swarm

# Create tmux session with 5 panes (adding coordinator pane)
tmux new-session -d -s "$SESSION_NAME" -x 150 -y 40

# Split into 4 panes
tmux split-window -h -t "$SESSION_NAME"
tmux split-window -v -t "$SESSION_NAME:0.0"
tmux split-window -v -t "$SESSION_NAME:0.1"

# Set working directory for all panes
for pane in 0 1 2 3; do
    tmux send-keys -t "$SESSION_NAME:0.$pane" "cd '$PROJECT_ROOT'" C-m
done

# Start primary swarm (pane 0)
log_info "Starting primary development swarm..."
tmux send-keys -t "$SESSION_NAME:0.0" "while true; do npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/primary-swarm.txt')\" --claude || echo 'Primary swarm restarting...'; sleep 5; done" C-m

# Start secondary swarm (pane 1) 
log_info "Starting secondary support swarm..."
tmux send-keys -t "$SESSION_NAME:0.1" "while true; do npx claude-flow@alpha swarm \"\$(cat '$PROJECT_ROOT/prompts/secondary-swarm.txt')\" --claude || echo 'Secondary swarm restarting...'; sleep 5; done" C-m

# Start artificial user (pane 2)
log_info "Starting artificial user agent..."
tmux send-keys -t "$SESSION_NAME:0.2" "'$SCRIPT_DIR/artificial-user.sh' '$LOG_DIR'" C-m

# Start health monitor (pane 3)
log_info "Starting health monitor..."
tmux send-keys -t "$SESSION_NAME:0.3" "'$SCRIPT_DIR/health-monitor.sh' '$LOG_DIR'" C-m

log_info "Perpetual Worker system started successfully!"
log_info "View with: tmux attach -t $SESSION_NAME"
log_info "Stop with: $SCRIPT_DIR/stop-perpetual.sh"

# Give user instructions
echo ""
echo "🎉 Perpetual Worker is now running!"
echo ""
echo "📺 View the system:"
echo "   tmux attach -t $SESSION_NAME"
echo ""
echo "🛑 Stop the system:"
echo "   $PROJECT_ROOT/stop-perpetual.sh"
echo ""
echo "📊 Check status:"
echo "   $SCRIPT_DIR/status.sh"
