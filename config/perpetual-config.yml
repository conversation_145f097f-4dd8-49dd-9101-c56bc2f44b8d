# Perpetual Worker Configuration

project:
  name: "Perpetual Worker Project"
  type: "general"  # web-app, api, library, general
  priority_file: "README.md"  # File containing project priorities
  
swarms:
  primary:
    max_agents: 5
    strategy: "auto"
    mode: "centralized"
    timeout_minutes: 60
    
  secondary:
    max_agents: 5
    strategy: "support"
    mode: "distributed"
    timeout_minutes: 45
    specialized_roles:
      - "qa_coordinator"
      - "documentation_lead" 
      - "test_engineer"
      - "standards_checker"
      - "support_analyst"

monitoring:
  health_check_interval: 60  # seconds
  max_restart_attempts: 3
  log_retention_days: 7
  process_threshold: 2  # minimum processes before restart

session:
  name: "perpetual-worker"
  auto_attach: false
  
automation:
  cronjob: true
  startup_delay: 10  # seconds
  recovery_delay: 5   # seconds between restart attempts
