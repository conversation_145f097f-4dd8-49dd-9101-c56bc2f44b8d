#!/usr/bin/env bats

# Unit tests for perpetual-orchestrator.sh

setup() {
  # Set up test environment
  export MIDNIGHT_BURNER_ROOT="/home/<USER>/github/midnight-burner"
  export TEST_MODE=1
  export PRIMARY_SWARM_FILE="$MIDNIGHT_BURNER_ROOT/prompts/primary-swarm.txt"
  export SECONDARY_SWARM_FILE="$MIDNIGHT_BURNER_ROOT/prompts/secondary-swarm.txt"
  export CONFIG_FILE="$MIDNIGHT_BURNER_ROOT/config/perpetual-config.yml"
}

teardown() {
  # Clean up test environment
  unset TEST_MODE
  unset MIDNIGHT_BURNER_ROOT
}

@test "orchestrator script exists and is executable" {
  [ -f "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" ]
  [ -x "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" ]
}

@test "orchestrator checks for required files" {
  # Test with missing primary swarm file
  mv "$PRIMARY_SWARM_FILE" "$PRIMARY_SWARM_FILE.bak" 2>/dev/null || true
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-files
  [ "$status" -ne 0 ]
  [[ "$output" =~ "primary-swarm.txt" ]]
  mv "$PRIMARY_SWARM_FILE.bak" "$PRIMARY_SWARM_FILE" 2>/dev/null || true
}

@test "orchestrator validates configuration file" {
  # Test with valid config
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --validate-config
  [ "$status" -eq 0 ]
}

@test "orchestrator creates log directory if missing" {
  # Remove log directory
  rm -rf "$MIDNIGHT_BURNER_ROOT/logs"
  
  # Run orchestrator in test mode
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --init-logs
  
  # Verify log directory was created
  [ -d "$MIDNIGHT_BURNER_ROOT/logs" ]
}

@test "orchestrator handles tmux session check" {
  # Test when tmux session doesn't exist
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-tmux
  
  # Should either find existing session or report it doesn't exist
  [[ "$output" =~ "tmux" ]] || [ "$status" -eq 1 ]
}

@test "orchestrator validates environment variables" {
  # Test with missing ANTHROPIC_API_KEY
  unset ANTHROPIC_API_KEY
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-env
  [ "$status" -ne 0 ]
  [[ "$output" =~ "ANTHROPIC_API_KEY" ]]
}

@test "orchestrator help message displays correctly" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --help
  [ "$status" -eq 0 ]
  [[ "$output" =~ "Midnight Burner Perpetual Orchestrator" ]]
  [[ "$output" =~ "Usage:" ]]
}

@test "orchestrator version displays correctly" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --version
  [ "$status" -eq 0 ]
  [[ "$output" =~ "1.0" ]]
}

@test "orchestrator handles invalid arguments" {
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --invalid-arg
  [ "$status" -ne 0 ]
  [[ "$output" =~ "Unknown option" ]] || [[ "$output" =~ "Invalid" ]]
}

@test "orchestrator creates state directory" {
  # Remove state directory
  rm -rf "$MIDNIGHT_BURNER_ROOT/state"
  
  # Run orchestrator state initialization
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --init-state
  
  # Verify state directory was created
  [ -d "$MIDNIGHT_BURNER_ROOT/state" ]
}

@test "orchestrator initializes secondary swarm correctly" {
  # Test secondary swarm initialization
  export TEST_MODE=1
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --init-secondary
  
  # Should reference task-structure-manager.sh
  [[ "$output" =~ "secondary swarm" ]] || [ "$status" -eq 0 ]
}

@test "orchestrator handles missing prompts directory" {
  # Temporarily rename prompts directory
  mv "$MIDNIGHT_BURNER_ROOT/prompts" "$MIDNIGHT_BURNER_ROOT/prompts.bak" 2>/dev/null || true
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-prompts
  [ "$status" -ne 0 ]
  
  # Restore prompts directory
  mv "$MIDNIGHT_BURNER_ROOT/prompts.bak" "$MIDNIGHT_BURNER_ROOT/prompts" 2>/dev/null || true
}

@test "orchestrator validates agent count configuration" {
  # Test with valid agent counts
  export PRIMARY_AGENTS=5
  export SECONDARY_AGENTS=5
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --validate-agents
  [ "$status" -eq 0 ] || [[ "$output" =~ "agents" ]]
}

@test "orchestrator handles invalid agent count" {
  # Test with invalid agent count
  export PRIMARY_AGENTS=0
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --validate-agents
  [ "$status" -ne 0 ] || [[ "$output" =~ "invalid" ]]
}

@test "orchestrator creates required log files" {
  # Clear log directory
  rm -f "$MIDNIGHT_BURNER_ROOT/logs/orchestrator.log"
  
  # Run orchestrator logging function
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --init-logs
  
  # Verify log file exists
  [ -f "$MIDNIGHT_BURNER_ROOT/logs/orchestrator.log" ] || [ "$status" -eq 0 ]
}

@test "orchestrator handles concurrent session attempts" {
  # Simulate existing session
  export TMUX_SESSION_EXISTS=1
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-session
  
  # Should detect existing session
  [[ "$output" =~ "already exists" ]] || [ "$status" -eq 1 ]
}

@test "orchestrator validates script dependencies" {
  # Check for required scripts
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-deps
  
  # Should verify all required scripts exist
  [ "$status" -eq 0 ] || [[ "$output" =~ "missing" ]]
}

@test "orchestrator handles signal interrupts gracefully" {
  # Test SIGINT handling
  export TEST_MODE=1
  export HANDLE_SIGNAL=INT
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --test-signal
  
  # Should handle signal appropriately
  [ "$status" -eq 0 ] || [[ "$output" =~ "interrupt" ]]
}

@test "orchestrator validates working directory" {
  # Test from different directory
  cd /tmp
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-pwd
  
  # Should handle directory change
  [ "$status" -eq 0 ] || [[ "$output" =~ "directory" ]]
  
  cd "$MIDNIGHT_BURNER_ROOT"
}

@test "orchestrator handles missing configuration file" {
  # Move config file temporarily
  mv "$CONFIG_FILE" "$CONFIG_FILE.bak" 2>/dev/null || true
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-config
  
  # Should handle missing config
  [ "$status" -ne 0 ] || [[ "$output" =~ "config" ]]
  
  # Restore config
  mv "$CONFIG_FILE.bak" "$CONFIG_FILE" 2>/dev/null || true
}

@test "orchestrator validates tmux availability" {
  # Check if tmux is installed
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-tmux-installed
  
  # Should verify tmux is available
  [ "$status" -eq 0 ] || [[ "$output" =~ "tmux" ]]
}

@test "orchestrator handles health check interval configuration" {
  # Test with custom interval
  export HEALTH_CHECK_INTERVAL=30
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --validate-interval
  
  # Should accept valid interval
  [ "$status" -eq 0 ] || [[ "$output" =~ "interval" ]]
}

@test "orchestrator validates npx availability" {
  # Check if npx is available
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-npx
  
  # Should verify npx is available
  [ "$status" -eq 0 ] || [[ "$output" =~ "npx" ]]
}

@test "orchestrator handles session name conflicts" {
  # Test with custom session name
  export SESSION_NAME="test-session-$$"
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-session-name
  
  # Should handle session name
  [ "$status" -eq 0 ] || [[ "$output" =~ "session" ]]
}

@test "orchestrator validates claude-flow installation" {
  # Check for claude-flow availability
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-claude-flow
  
  # Should verify claude-flow
  [ "$status" -eq 0 ] || [[ "$output" =~ "claude-flow" ]]
}

@test "orchestrator handles permission issues" {
  # Test with read-only directory
  chmod -w "$MIDNIGHT_BURNER_ROOT/logs" 2>/dev/null || true
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --check-permissions
  
  # Should detect permission issue
  [ "$status" -ne 0 ] || [[ "$output" =~ "permission" ]]
  
  # Restore permissions
  chmod +w "$MIDNIGHT_BURNER_ROOT/logs" 2>/dev/null || true
}

@test "orchestrator validates pane configuration" {
  # Test pane setup validation
  export EXPECTED_PANES=4
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --validate-panes
  
  # Should validate pane configuration
  [ "$status" -eq 0 ] || [[ "$output" =~ "panes" ]]
}

@test "orchestrator handles script execution errors" {
  # Test with non-existent script
  export TEST_SCRIPT="/nonexistent/script.sh"
  
  run bash "$MIDNIGHT_BURNER_ROOT/scripts/perpetual-orchestrator.sh" --test-exec
  
  # Should handle execution error
  [ "$status" -ne 0 ] || [[ "$output" =~ "error" ]]
}