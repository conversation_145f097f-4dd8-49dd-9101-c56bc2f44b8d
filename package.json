{"name": "midnight-burner", "version": "1.0.0", "description": "The AI that never sleeps - Turn any repository into a self-sustaining, 24/7 development machine", "main": "install.sh", "scripts": {"install": "./install.sh", "start": "./start-midnight-burner.sh", "stop": "./stop-midnight-burner.sh", "restart": "./restart-midnight-burner.sh", "status": "./scripts/status.sh", "test": "echo 'Run ./test-midnight-burner.sh to test the system'"}, "keywords": ["ai", "automation", "development", "claude-flow", "midnight-burner", "autonomous", "swarm", "continuous-integration", "devops"], "author": "Midnight Burner Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/m-check1B/midnight-burner.git"}, "bugs": {"url": "https://github.com/m-check1B/midnight-burner/issues"}, "homepage": "https://github.com/m-check1B/midnight-burner#readme", "engines": {"node": ">=18.0.0"}, "dependencies": {"claude-flow": "^2.0.0-alpha.84"}, "peerDependencies": {"tmux": "*", "git": "*"}, "bin": {"midnight-burner": "./install.sh"}}