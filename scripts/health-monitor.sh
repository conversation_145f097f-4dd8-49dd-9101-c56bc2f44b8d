#!/usr/bin/env bash
# Health Monitor - Tracks system performance and recovery

LOG_DIR="${1:-logs}"
SESSION_NAME="${SESSION_NAME:-perpetual-worker}"
HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-60}"

mkdir -p "$LOG_DIR"

log_health() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_DIR/health-monitor.log"
}

log_health "Health monitor started (check interval: ${HEALTH_CHECK_INTERVAL}s)"

while true; do
    # System health checks
    process_count=$(pgrep -f "claude-flow" | wc -l)
    memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    load_average=$(uptime | awk -F'load average:' '{print $2}' | cut -d',' -f1 | tr -d ' ')
    
    # Log health metrics
    log_health "Processes: $process_count, Memory: ${memory_usage}%, Load: $load_average"
    
    # Display in monitor pane
    clear
    echo "🔄 Perpetual Worker - Health Monitor"
    echo "=================================="
    echo ""
    echo "📊 System Status:"
    echo "   Claude Flow Processes: $process_count"
    echo "   Memory Usage: ${memory_usage}%"
    echo "   Load Average: $load_average"
    echo "   Last Check: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo "📋 Active Components:"
    if tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
        echo "   ✅ Primary Swarm (Pane 0)"
        echo "   ✅ Secondary Swarm (Pane 1)"
        echo "   ✅ Artificial User (Pane 2)" 
        echo "   ✅ Health Monitor (Pane 3)"
    else
        echo "   ❌ Session not found"
    fi
    echo ""
    echo "🔄 Next check in ${HEALTH_CHECK_INTERVAL} seconds..."
    
    sleep "$HEALTH_CHECK_INTERVAL"
done
