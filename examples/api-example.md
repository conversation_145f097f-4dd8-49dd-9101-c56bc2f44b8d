# Perpetual Worker - API Example

This example shows how to set up Perpetual Worker for a backend API project.

## Project Type: REST API / GraphQL API

### 1. Installation
```bash
git clone https://github.com/m-check1B/perpetual-worker.git
cd perpetual-worker
./install.sh
```

### 2. Configure for API Development
Edit `config/perpetual-config.yml`:
```yaml
project:
  name: "My API"
  type: "api"
  priority_file: "API_TODOS.md"

swarms:
  primary:
    max_agents: 5
    focus: "development"  # Focus on API endpoints
    
  secondary:
    max_agents: 4
    focus: "testing"      # Focus on API testing
```

### 3. Customize Prompts for API Development
Edit `prompts/primary-swarm.txt`:
```
🎯 API DEVELOPMENT PRIORITIES:
1. **Endpoints**: Build RESTful API endpoints with proper HTTP methods
2. **Data Models**: Design and implement database models and schemas
3. **Authentication**: Implement secure authentication and authorization
4. **Validation**: Add request validation and error handling
5. **Documentation**: Maintain OpenAPI/Swagger documentation
6. **Performance**: Optimize database queries and caching
```

### 4. Project Structure
Ensure your API project has:
```
my-api/
├── README.md              # Project overview
├── API_TODOS.md          # API-specific todo items
├── package.json          # Dependencies
├── src/
│   ├── routes/           # API route handlers
│   ├── models/           # Database models
│   ├── middleware/       # Express middleware
│   ├── services/         # Business logic
│   └── utils/            # Utility functions
├── tests/
│   ├── unit/             # Unit tests
│   ├── integration/      # API integration tests
│   └── fixtures/         # Test data
├── docs/
│   ├── api.md            # API documentation
│   └── openapi.yml       # OpenAPI specification
└── scripts/              # Build and deployment scripts
```

### 5. Create API_TODOS.md
```markdown
# API Development Priorities

## High Priority
- [ ] User authentication endpoints (POST /auth/login, /auth/register)
- [ ] User profile management (GET/PUT /users/:id)
- [ ] Input validation for all endpoints
- [ ] Error handling middleware

## Medium Priority  
- [ ] Password reset functionality
- [ ] Email verification system
- [ ] Rate limiting implementation
- [ ] API versioning strategy

## Low Priority
- [ ] Advanced filtering and pagination
- [ ] Caching layer implementation
- [ ] Performance monitoring
- [ ] Load testing setup
```

### 6. Start Development
```bash
./start-perpetual.sh
```

## Expected API Development Flow

The system will:
1. **Read API_TODOS.md** to understand priorities
2. **Implement endpoints** with proper HTTP methods and status codes
3. **Add validation** using libraries like Joi or express-validator
4. **Write tests** for each endpoint using Jest/Mocha
5. **Update documentation** in OpenAPI format
6. **Create PRs** with related endpoints, tests, and docs

## Typical API Tasks

The perpetual system will work on:
- Creating new REST endpoints
- Adding database models and migrations
- Implementing authentication middleware
- Writing comprehensive API tests
- Adding request/response validation
- Updating API documentation
- Optimizing database queries
- Adding error handling
- Implementing caching strategies

## API-Specific Monitoring

The health monitor tracks:
- API response times
- Error rates by endpoint
- Database query performance
- Memory usage and CPU load
- Test coverage for endpoints

## Best Practices for API Projects

1. **Maintain API_TODOS.md** with specific endpoint requirements
2. **Follow REST conventions** for HTTP methods and status codes
3. **Write tests first** for each new endpoint
4. **Keep OpenAPI spec updated** with all endpoints
5. **Use proper error handling** with consistent error formats
6. **Implement proper logging** for debugging and monitoring
7. **Add rate limiting** to prevent abuse
8. **Use environment variables** for configuration

## Example PR Structure

A typical PR from the perpetual system might include:
```
PR: Add user profile management endpoints

Files changed:
- src/routes/users.js (new user endpoints)
- src/models/User.js (user model updates)
- tests/integration/users.test.js (endpoint tests)
- docs/openapi.yml (API documentation)
- PR_NOTES.md (implementation details)
```