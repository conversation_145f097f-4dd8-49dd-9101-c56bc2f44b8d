#!/usr/bin/env bats

# Integration tests for system startup sequence

setup() {
  export MIDNIGHT_BURNER_ROOT="/home/<USER>/github/midnight-burner"
  export TEST_MODE=1
  export STARTUP_TIMEOUT=10
  
  # Ensure clean state
  tmux kill-session -t test-perpetual-worker 2>/dev/null || true
}

teardown() {
  # Clean up test sessions
  tmux kill-session -t test-perpetual-worker 2>/dev/null || true
  unset TEST_MODE
}

@test "complete startup sequence executes successfully" {
  # Test startup with test session name
  export TMUX_SESSION="test-perpetual-worker"
  
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode
  
  # Should complete without errors
  [ "$status" -eq 0 ] || [[ "$output" =~ "already running" ]]
  
  # Verify tmux session created
  run tmux has-session -t test-perpetual-worker
  [ "$status" -eq 0 ] || [[ "$output" =~ "perpetual-worker" ]]
}

@test "all required files are checked during startup" {
  # Temporarily rename a required file
  mv "$MIDNIGHT_BURNER_ROOT/prompts/primary-swarm.txt" \
     "$MIDNIGHT_BURNER_ROOT/prompts/primary-swarm.txt.bak" 2>/dev/null || true
  
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --check-only
  
  # Should fail due to missing file
  [ "$status" -ne 0 ]
  [[ "$output" =~ "primary-swarm.txt" ]]
  
  # Restore file
  mv "$MIDNIGHT_BURNER_ROOT/prompts/primary-swarm.txt.bak" \
     "$MIDNIGHT_BURNER_ROOT/prompts/primary-swarm.txt" 2>/dev/null || true
}

@test "environment variables are validated" {
  # Unset required variable
  unset ANTHROPIC_API_KEY
  
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --validate-env
  
  # Should fail with clear message
  [ "$status" -ne 0 ]
  [[ "$output" =~ "ANTHROPIC_API_KEY" ]]
}

@test "tmux panes are created correctly" {
  export TMUX_SESSION="test-perpetual-worker"
  
  # Start system
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Check pane count
  pane_count=$(tmux list-panes -t test-perpetual-worker 2>/dev/null | wc -l)
  [ "$pane_count" -eq 4 ] || [ "$pane_count" -eq 0 ]
}

@test "log files are created during startup" {
  # Remove existing logs
  rm -rf "$MIDNIGHT_BURNER_ROOT/logs"
  
  # Start system
  export TMUX_SESSION="test-perpetual-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Verify logs directory exists
  [ -d "$MIDNIGHT_BURNER_ROOT/logs" ]
  
  # Check for specific log files
  [ -f "$MIDNIGHT_BURNER_ROOT/logs/orchestrator.log" ] || [ -f "$MIDNIGHT_BURNER_ROOT/logs/startup.log" ]
}

@test "configuration is loaded correctly" {
  # Create test config
  cat > "$MIDNIGHT_BURNER_ROOT/config/test-config.yml" <<EOF
test_mode: true
response_interval: 5
health_check_interval: 10
EOF
  
  export CONFIG_FILE="$MIDNIGHT_BURNER_ROOT/config/test-config.yml"
  
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --load-config
  
  # Should load without errors
  [ "$status" -eq 0 ] || [[ "$output" =~ "config" ]]
  
  # Cleanup
  rm -f "$MIDNIGHT_BURNER_ROOT/config/test-config.yml"
}

@test "startup handles existing sessions gracefully" {
  export TMUX_SESSION="test-perpetual-worker"
  
  # Create existing session
  tmux new-session -d -s test-perpetual-worker
  
  # Try to start again
  run bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode
  
  # Should detect existing session
  [[ "$output" =~ "already running" ]] || [[ "$output" =~ "exists" ]]
}

@test "state directory is initialized" {
  # Remove state directory
  rm -rf "$MIDNIGHT_BURNER_ROOT/state"
  
  # Start system
  export TMUX_SESSION="test-perpetual-worker"
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  
  # Verify state directory created
  [ -d "$MIDNIGHT_BURNER_ROOT/state" ]
}

@test "processes start in correct order" {
  export TMUX_SESSION="test-perpetual-worker"
  export STARTUP_LOG="$MIDNIGHT_BURNER_ROOT/logs/startup-test.log"
  
  # Start with logging
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode --log-startup 2>&1 | tee "$STARTUP_LOG"
  
  # Verify startup order (if log exists)
  if [ -f "$STARTUP_LOG" ]; then
    # Primary swarm should start before secondary
    primary_line=$(grep -n "primary" "$STARTUP_LOG" | head -1 | cut -d: -f1)
    secondary_line=$(grep -n "secondary" "$STARTUP_LOG" | head -1 | cut -d: -f1)
    
    # If both found, primary should be first
    if [ -n "$primary_line" ] && [ -n "$secondary_line" ]; then
      [ "$primary_line" -lt "$secondary_line" ]
    fi
  fi
  
  # Cleanup
  rm -f "$STARTUP_LOG"
}

@test "startup script is idempotent" {
  export TMUX_SESSION="test-perpetual-worker"
  
  # Run startup twice
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  first_result=$?
  
  bash "$MIDNIGHT_BURNER_ROOT/start-perpetual.sh" --test-mode || true
  second_result=$?
  
  # Both should succeed or report already running
  [ "$first_result" -eq 0 ] || [ "$second_result" -eq 0 ]
}

@test "CLI tool integration with startup" {
  export TEST_PROJECT="/tmp/test-cli-project-$$"
  mkdir -p "$TEST_PROJECT"
  echo "# Test" > "$TEST_PROJECT/README.md"
  
  # Initialize project with CLI
  run bash "$MIDNIGHT_BURNER_ROOT/midnight-burner-cli.sh" init "$TEST_PROJECT"
  [ "$status" -eq 0 ]
  
  # Clean up
  rm -rf "$TEST_PROJECT"
}

@test "safety mechanisms prevent midnight-burner modification" {
  export TMUX_SESSION="test-perpetual-worker"
  export TEST_PROJECT="$MIDNIGHT_BURNER_ROOT"  # Try to use midnight-burner itself
  
  # Should prevent using midnight-burner as target
  run bash "$MIDNIGHT_BURNER_ROOT/midnight-burner-cli.sh" init "$TEST_PROJECT"
  
  # Should fail or warn
  [ "$status" -ne 0 ] || [[ "$output" =~ "cannot" ]] || [[ "$output" =~ "midnight-burner" ]]
}

@test "linear integration check" {
  # Check if Linear MCP integration script exists
  if [ -f "$MIDNIGHT_BURNER_ROOT/scripts/linear-mcp-integration.sh" ]; then
    run bash "$MIDNIGHT_BURNER_ROOT/scripts/linear-mcp-integration.sh" --check
    
    # Should at least run without crash
    [ "$status" -eq 0 ] || [[ "$output" =~ "LINEAR_API_KEY" ]] || [[ "$output" =~ "not configured" ]]
  else
    skip "Linear integration not present"
  fi
}