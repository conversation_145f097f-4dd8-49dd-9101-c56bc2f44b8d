# Perpetual Worker Configuration
# Customize this file for your project needs

project:
  name: "My Perpetual Project"
  type: "general"  # Options: web-app, api, library, general, mobile
  priority_file: "README.md"  # File containing project priorities/todos
  
swarms:
  primary:
    max_agents: 5
    strategy: "auto"
    mode: "centralized"
    timeout_minutes: 60
    focus: "development"  # development, features, bugs, refactor
    
  secondary:
    max_agents: 3
    strategy: "support"
    mode: "distributed"
    timeout_minutes: 45
    focus: "quality"  # testing, docs, review, performance

monitoring:
  health_check_interval: 60  # seconds between health checks
  max_restart_attempts: 3
  log_retention_days: 7
  process_threshold: 2  # minimum claude-flow processes before restart
  memory_threshold: 80  # percent memory usage before warning

session:
  name: "perpetual-worker"
  auto_attach: false
  pane_layout: "quad"  # quad, horizontal, vertical
  
automation:
  cronjob: true  # Install cronjobs for auto-startup
  startup_delay: 10  # seconds to wait before starting swarms
  recovery_delay: 5   # seconds between restart attempts
  
pr_generation:
  enabled: true
  min_files: 3  # minimum files before creating PR
  max_files: 8  # maximum files in one PR
  include_notes: true  # create PR_NOTES.md files
  
# Custom responses for artificial user
responses:
  encouragement:
    - "Continue with the next priority task"
    - "Analyze current progress and implement next feature"
    - "Work on the active development tasks systematically"
    - "Check project status and proceed with development"
    - "Keep building - focus on high priority items"
  
  feedback:
    - "Good progress, continue with next phase"
    - "Proceed with the implementation"
    - "Continue development workflow"
    - "That looks correct, keep going"
    - "Excellent work, move to next task"